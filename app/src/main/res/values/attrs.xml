<resources>
    <declare-styleable name="PagerSlidingTabStrip">
        <attr name="pstsIndicatorColor" format="color" />
        <attr name="pstsUnderlineColor" format="color" />
        <attr name="pstsDividerColor" format="color" />
        <attr name="pstsTabTextColor" format="color" />
        <attr name="pstsIndicatorHeight" format="dimension" />
        <attr name="pstsUnderlineHeight" format="dimension" />
        <attr name="pstsDividerPadding" format="dimension" />
        <attr name="pstsTabPaddingLeftRight" format="dimension" />
        <attr name="pstsScrollOffset" format="dimension" />
        <attr name="pstsTabTextSize" format="dimension" />
        <attr name="pstsTabBackground" format="reference" />
        <attr name="pstsShouldExpand" format="boolean" />
        <attr name="pstsTextAllCaps" format="boolean" />
    </declare-styleable>

    <declare-styleable name="MarqueeViewStyle">
        <attr name="mvInterval" format="integer|reference" />
        <attr name="mvAnimDuration" format="integer|reference" />
        <attr name="mvTextSize" format="dimension|reference" />
        <attr name="mvTextColor" format="color|reference" />
        <attr name="mvGravity">
            <enum name="left" value="0" />
            <enum name="center" value="1" />
            <enum name="right" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="MarqueeViewSpellGroup">
        <attr name="isSetAnimDuration" format="boolean" />
        <attr name="isSingleLine" format="boolean" />
        <attr name="marquee_count" format="integer" />
        <attr name="marquee_interval" format="integer|reference" />
        <attr name="marquee_animDuration" format="integer|reference" />
        <attr name="marquee_textSize" format="dimension|reference" />
        <attr name="marquee_textColor" format="color|reference" />
        <attr name="isFlippingLessCount" format="boolean" />
    </declare-styleable>

    <!-- 广告viewpager扩展字段 -->
    <declare-styleable name="MyAdViewPager">

        <!-- 未选中指示器 -->
        <attr name="indicator_" format="reference" />
        <!-- 选中指示器 -->
        <attr name="indicator_focused_" format="reference" />
        <!-- 是否滑动 -->
        <attr name="slide_" format="boolean" />
        <!-- 是否轮播 -->
        <attr name="play_" format="boolean" />
        <!-- 是否显示指示器 -->
        <attr name="show_indicator_" format="boolean" />
        <!-- 指示器的位置 0,左边，1中间，右边 -->
        <attr name="location_indicator_" format="integer" />
        <!-- 背景 -->
        <attr name="bg_" format="reference" />
    </declare-styleable>


    <declare-styleable name="TagGroup">
        <!-- Whether the tag groups is in append mode. -->
        <attr name="atg_isAppendMode" format="boolean" />
        <!-- If the tag groups is in append mode, what the hint of input tag. -->
        <attr name="atg_inputHint" format="string" />

        <!-- The tag view outline border color. -->
        <attr name="atg_borderColor" format="color" />


        <attr name="atg_maxLine" format="integer" />

        <!-- The tag view text color. -->
        <attr name="atg_textColor" format="color" />
        <!-- The tag view background color. -->
        <attr name="atg_backgroundColor" format="color" />

        <!-- The dash outline border color, when in append mode. -->
        <attr name="atg_dashBorderColor" format="color" />
        <!-- The input tag hint text color, when in append mode. -->
        <attr name="atg_inputHintColor" format="color" />
        <!-- The input tag type text color, when in append mode. -->
        <attr name="atg_inputTextColor" format="color" />

        <!-- The checked tag view outline border color. -->
        <attr name="atg_checkedBorderColor" format="color" />
        <!-- The checked text color. -->
        <attr name="atg_checkedTextColor" format="color" />
        <!-- The checked marker color. -->
        <attr name="atg_checkedMarkerColor" format="color" />
        <!-- The checked tag view background color. -->
        <attr name="atg_checkedBackgroundColor" format="color" />

        <attr name="atg_pressedBackgroundColor" format="color" />

        <!-- The tag view background color, when the tag view is being pressed. -->
        <attr name="atg_radius" format="dimension" />

        <!-- The tag view outline border stroke width. -->
        <attr name="atg_borderStrokeWidth" format="dimension" />
        <attr name="atg_maxWidth" format="dimension" />
        <attr name="atg_maxHeight" format="dimension" />
        <!-- The tag view text size. -->
        <attr name="atg_textSize" format="dimension" />

        <attr name="atg_horizontalSpacing" format="dimension" />
        <attr name="atg_verticalSpacing" format="dimension" />
        <attr name="atg_horizontalPadding" format="dimension" />
        <attr name="atg_verticalPadding" format="dimension" />
    </declare-styleable>

    <declare-styleable name="Themes">
        <attr name="tagGroupStyle" format="reference" />
    </declare-styleable>

    <declare-styleable name="CustomProgressBar">
        <attr name="progressColor" format="color" />
        <attr name="borderColor" format="color" />
        <attr name="progressDescColor" format="color" />
        <attr name="progressRadius" format="dimension" />
        <attr name="max" format="integer" />
        <attr name="progress" format="integer" />
        <attr name="border_Width" format="dimension" />
        <attr name="descPadding" format="dimension" />
        <attr name="descTextSize" format="dimension" />
        <attr name="progressDesc" format="string" />
        <attr name="isShowDesc" format="boolean" />
    </declare-styleable>
    <declare-styleable name="square_imageview">
        <attr name="base_on" format="integer">
            <enum name="auto" value="0" />
            <enum name="width" value="1" />
            <enum name="height" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="SwipeMenuLayout">
        <attr name="swipeEnable" format="boolean" />
        <attr name="ios" format="boolean" />
        <attr name="leftSwipe" format="boolean" />
    </declare-styleable>

    <!--文字标签被文字包裹-->
    <declare-styleable name="SurroundTagView">
        <attr name="tag_color" format="color" />
        <attr name="tag_background" format="color" />
        <attr name="tag_text" format="string|integer" />
        <attr name="content_text" format="string|integer" />
    </declare-styleable>

    <declare-styleable name="AndroidTagView">
        <attr name="vertical_interval" format="dimension" />
        <attr name="horizontal_interval" format="dimension" />

        <attr name="container_border_width" format="dimension" />
        <attr name="container_border_radius" format="dimension" />
        <attr name="container_border_color" format="color" />
        <attr name="container_background_color" format="color" />
        <attr name="container_enable_drag" format="boolean" />
        <attr name="container_drag_sensitivity" format="float" />
        <attr name="container_max_lines" format="integer" />
        <attr name="container_gravity" format="enum">
            <enum name="left" value="3" />
            <enum name="center" value="17" />
            <enum name="right" value="5" />
        </attr>

        <attr name="tag_border_width" format="dimension" />
        <attr name="tag_corner_radius" format="dimension" />
        <attr name="tag_horizontal_padding" format="dimension" />
        <attr name="tag_vertical_padding" format="dimension" />
        <attr name="tag_text_size" format="dimension" />
        <attr name="tag_bd_distance" format="dimension" />
        <attr name="tag_text_color" format="color" />
        <attr name="tag_border_color" format="color" />
        <attr name="tag_background_color" format="color" />
        <attr name="tag_max_length" format="integer" />
        <attr name="tag_clickable" format="boolean" />
        <attr name="tag_selectable" format="boolean" />
        <attr name="tag_theme" format="enum">
            <enum name="none" value="-1" />
            <enum name="random" value="0" />
            <enum name="pure_cyan" value="1" />
            <enum name="pure_teal" value="2" />
        </attr>
        <attr name="tag_text_direction" format="enum">
            <enum name="ltr" value="3" />
            <enum name="rtl" value="4" />
        </attr>

        <attr name="tag_ripple_color" format="color" />
        <attr name="tag_ripple_alpha" format="integer" />
        <attr name="tag_ripple_duration" format="integer" />

        <attr name="tag_enable_cross" format="boolean" />
        <attr name="tag_cross_width" format="dimension" />
        <attr name="tag_cross_color" format="color" />
        <attr name="tag_cross_line_width" format="dimension" />
        <attr name="tag_cross_area_padding" format="dimension" />
        <attr name="tag_support_letters_rlt" format="boolean" />
        <attr name="tag_background_bg" format="color" />
    </declare-styleable>

    <declare-styleable name="SwitchButton">
        <attr name="sb_openBackground" format="color" />
        <attr name="sb_closeBackground" format="color" />
        <attr name="sb_circleColor" format="color" />
        <attr name="sb_circleRadius" format="dimension" />
        <attr name="sb_status">
            <enum name="close" value="0" />
            <enum name="open" value="1" />
        </attr>
        <attr name="sb_interpolator">
            <enum name="Linear" value="0" />
            <enum name="Overshoot" value="1" />
            <enum name="Accelerate" value="2" />
            <enum name="Decelerate" value="3" />
            <enum name="AccelerateDecelerate" value="4" />
            <enum name="LinearOutSlowIn" value="5" />
        </attr>
    </declare-styleable>

    <!--自定义View - 手机验证码输入框-->
    <declare-styleable name="VerificationView">
        <attr name="vTextSize" format="dimension" />
        <attr name="vTextCount" format="integer" />
        <attr name="vTextColor" format="color" />
        <attr name="vBackgroundResource" format="reference" />
        <attr name="vCursorDrawable" format="reference" />
        <attr name="vBackgroundColor" format="color" />
        <attr name="vAutoShowInputBoard" format="boolean" />
        <attr name="vWidth" format="dimension" />
        <attr name="vWidthPercent" format="float" />
        <attr name="vLineColor" format="color" />
        <attr name="vLineHeight" format="dimension" />
    </declare-styleable>

    <declare-styleable name="Theme">
        <attr name="cornerImageViewStyle" format="reference" />
    </declare-styleable>
    <!-- 自定义圆角ImageView -->
    <declare-styleable name="CornerImageView">
        <attr name="circle" format="boolean" />
        <attr name="imgRadius" format="dimension" />
    </declare-styleable>

    <!--小直播-->
    <!-- 飘星自定义属性 -->
    <declare-styleable name="HeartLayout">
        <attr name="initX" format="dimension" />
        <attr name="initY" format="dimension" />
        <attr name="xRand" format="dimension" />
        <attr name="animLengthRand" format="dimension" />
        <attr name="xPointFactor" format="dimension" />
        <attr name="animLength" format="dimension" />
        <attr name="heart_width" format="dimension" />
        <attr name="heart_height" format="dimension" />
        <attr name="bezierFactor" format="integer" />
        <attr name="anim_duration" format="integer" />
    </declare-styleable>
    <!-- 标题模板自定义属性 -->
    <declare-styleable name="TCActivityTitle">
        <!-- 标题文案 -->
        <attr name="titleText" format="string" />
        <!-- 是否有back按钮 -->
        <attr name="canBack" format="boolean" />
        <!-- back按钮文案 -->
        <attr name="backText" format="string" />
        <!-- 右侧更多功能按钮文字 -->
        <attr name="moreText" format="string" />
    </declare-styleable>


    <declare-styleable name="PromotionTagView">
        <attr name="subTitleTextSize" format="dimension" />
        <attr name="contentTextSize" format="dimension" />

        <attr name="shopTopTextSize" format="dimension" />
        <attr name="shopBottomTextSize" format="dimension" />
        <attr name="shopTimeTextSize" format="dimension" />
        <attr name="shopContentTextSize" format="dimension" />
    </declare-styleable>
    <!--    //recycleView滚动指示器-->
    <declare-styleable name="RvIndicatorView">
        <attr name="rv_indicator_layout_background" format="reference" />
        <attr name="rv_indicator_layout_width" format="dimension" />
        <attr name="rv_indicator_layout_height" format="dimension" />
        <attr name="rv_indicator_background" format="reference" />
        <attr name="rv_indicator_width" format="dimension" />
        <attr name="rv_indicator_height" format="dimension" />
    </declare-styleable>

    <declare-styleable name="Mine2CommonToolsProgressView">
        <attr name="barWidth" format="dimension" />
        <attr name="barCorner" format="dimension" />
        <attr name="barColor" format="color" />
        <attr name="barBackgroundColor" format="color"/>
        <attr name="barProgress" format="integer" />
    </declare-styleable>
</resources>