<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_no_start_spell_group_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="@dimen/dimen_dp_60"
    tools:visibility="visible"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_goods_detail_no_start_spell_group2"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingStart="10dp">

        <!-- 创建一个容器来包含拼团价标题和价格，用于垂直居中对齐 -->
        <LinearLayout
            android:id="@+id/ll_no_start_price_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 第一行：拼团价标题和价格 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:baselineAligned="true"
                android:gravity="bottom">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="拼团价"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:gravity="bottom"
                    android:layout_gravity="bottom" />

                <TextView
                    android:id="@+id/tv_no_start_spell_group_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_3"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:background="@drawable/shape_spell_group_price"
                    android:gravity="bottom"
                    android:layout_gravity="bottom"
                    tools:text="¥15.25" />

            </LinearLayout>

            <!-- 第二行：单价 -->
            <LinearLayout
                android:id="@+id/ll_no_start_second_line"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp"
                android:visibility="gone">

                <!-- 单价显示 -->
                <TextView
                    android:id="@+id/tv_no_start_unit_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="11sp"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    tools:text="单价"
                    tools:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

        <!-- 开抢时间 -->
        <TextView
            android:id="@+id/tv_no_start_spell_group_time"
            android:layout_width="170dp"
            android:layout_height="@dimen/dimen_dp_24"
            android:gravity="center_vertical"
            android:layout_marginStart="@dimen/dimen_dp_7"
            android:textColor="#FF9C4600"
            android:textSize="12sp"
            android:paddingStart="@dimen/dimen_dp_10"
            android:background="@drawable/shape_no_start_time_bg"
            tools:text="03月21日 18:00 开抢" />

    </LinearLayout>

</LinearLayout>