<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="6"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="60dp">

            <!--<View-->
            <!--style="@style/view_line_icon"-->
            <!--android:layout_marginRight="11dp"-->
            <!--android:layout_toLeftOf="@+id/tv"/>-->

            <TextView
                android:id="@+id/tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="物流方式"
                android:textColor="@color/text_292933"
                android:textSize="16dp" />

            <!--<View-->
            <!--style="@style/view_line_icon"-->
            <!--android:layout_marginLeft="11dp"-->
            <!--android:layout_toRightOf="@+id/tv"/>-->

            <ImageView
                android:id="@+id/mCloseIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:padding="10dp"
                android:src="@drawable/icon_detail_service_close" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/divider_line_base_1px"/>
        </RelativeLayout>

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="@color/activity_bg" />

    </LinearLayout>
</LinearLayout>