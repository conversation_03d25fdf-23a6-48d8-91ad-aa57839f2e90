<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_balance_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingBottom="12dp"
    android:paddingLeft="10dp"
    android:paddingRight="10dp"
    android:paddingTop="12dp">
    <TextView
        android:id="@+id/tv_balance_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:textColor="#292933"
        android:textSize="15sp"
        android:textStyle="bold"
        tools:text="+1000" />
    <TextView
        android:id="@+id/tv_balance_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_toEndOf="@+id/tv_balance_num"
        android:layout_centerVertical="true"
        android:textColor="#292933"
        android:textSize="12sp"
        tools:text="购物抵扣" />

    <TextView
        android:id="@+id/tv_balance_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@+id/iv_right"
        android:layout_marginEnd="4dp"
        android:layout_centerVertical="true"
        android:textColor="#9494A6"
        android:textSize="12sp"
        tools:text="2017-03-29 16:37:00" />



    <ImageView
        android:id="@+id/iv_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:src="@drawable/icon_my_balance_arrow_right" />
</RelativeLayout>