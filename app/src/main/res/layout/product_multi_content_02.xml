<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center_horizontal"
    android:minWidth="178dp"
    android:padding="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/home_white_product_bg"
        android:gravity="center_horizontal"
        android:minWidth="178dp"
        android:orientation="vertical"
        android:paddingBottom="4dp">

        <FrameLayout
            android:layout_width="104dp"
            android:layout_height="104dp">

            <ImageView
                android:id="@+id/iv_product"
                android:layout_width="104dp"
                android:layout_height="104dp"
                android:layout_marginTop="2dp"
                android:padding="@dimen/home_product_image_padding" />

            <ImageView
                android:id="@+id/iv_tag_left"
                android:layout_width="104dp"
                android:layout_height="104dp"
                android:layout_marginTop="2dp"
                android:scaleType="fitXY"
                android:src="@drawable/transparent" />

            <com.ybmmarket20.view.PromotionTagView
                android:id="@+id/view_ptv"
                android:layout_width="104dp"
                android:layout_height="104dp"
                app:subTitleTextSize="4dp"
                app:contentTextSize="8dp"
                android:layout_marginTop="2dp" />

            <TextView
                android:id="@+id/tv_activity_price"
                style="@style/activity_price"
                android:layout_height="13dp"
                android:text=""
                android:visibility="gone" />

        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:layout_marginTop="6dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/tv_procurement_festival"
                android:layout_width="42dp"
                android:layout_height="17dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/icon_procurement_festival"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_exclusive"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/bg_brand_item_exclusive"
                android:paddingLeft="2dp"
                android:paddingRight="2dp"
                android:text="独家"
                android:textColor="@color/white"
                android:textSize="11dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_health_insurance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/bg_brand_item_health_insurance"
                android:paddingLeft="2dp"
                android:paddingRight="2dp"
                android:text="医保"
                android:textColor="@color/white"
                android:textSize="11dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:text=""
                android:textColor="#ff000000"
                android:textSize="12dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_spec"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:paddingLeft="7dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/text_9494A6"
                android:textSize="11dp" />

            <TextView
                android:id="@+id/shop_price_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/brand_description_tv1"
                android:textSize="11dp"
                android:visibility="visible" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="end"
                android:lines="1"
                android:paddingLeft="7dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/record_red"
                android:textSize="14dp" />

            <com.ybmmarket20.view.ProductEditLayout
                android:id="@+id/el_edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone" />

        </LinearLayout>

        <com.ybmmarket20.view.TagView
            android:id="@+id/rl_icon_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="4dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/shop_no_limit_tv01"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginBottom="35dp"
        android:background="@drawable/shop_limit01"
        android:gravity="center"
        android:text=""
        android:textColor="#ffffff"
        android:textSize="12dp"
        android:visibility="invisible" />

</FrameLayout>