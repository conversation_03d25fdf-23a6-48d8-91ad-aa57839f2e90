<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="160dp"
    android:layout_height="246dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center_horizontal"
    android:paddingLeft="4dp"
    android:paddingTop="2dp"
    android:paddingRight="4dp"
    android:paddingBottom="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="152dp"
            android:layout_height="152dp"
            android:layout_marginTop="2dp">

            <ImageView
                android:id="@+id/iv_product"
                android:layout_width="152dp"
                android:layout_height="152dp"
                android:background="@drawable/home_image_bg"
                android:padding="@dimen/home_product_image_padding" />

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/home_product_image_padding"
                android:visibility="gone">


                <com.ybmmarket20.view.PromotionTagView
                    android:id="@+id/view_ptv"
                    android:layout_width="104dp"
                    android:layout_height="104dp"
                    app:subTitleTextSize="4dp"
                    app:contentTextSize="8dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="2dp" />

                <TextView
                    android:id="@+id/tv_activity_price"
                    style="@style/activity_price"
                    android:layout_height="13dp"
                    android:text=""
                    android:visibility="gone" />

            </FrameLayout>

        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/tv_procurement_festival"
                android:layout_width="42dp"
                android:layout_height="17dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/icon_procurement_festival"
                android:paddingLeft="2dp"
                android:paddingRight="2dp"
                android:textColor="@color/white"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_exclusive"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/bg_brand_item_exclusive"
                android:paddingLeft="2dp"
                android:paddingRight="2dp"
                android:text="独家"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_health_insurance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/bg_brand_item_health_insurance"
                android:paddingLeft="2dp"
                android:paddingRight="2dp"
                android:text="医保"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:text=""
                android:textColor="#ff000000"
                android:textSize="14sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_spec"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/text_9494A6"
            android:textSize="13sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingTop="2dp"
            android:paddingBottom="2dp">

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/record_red"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:ellipsize="end"
                android:lines="1"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/text_9494A6"
                android:textSize="14sp" />
        </LinearLayout>

        <com.ybmmarket20.view.TagView
            android:id="@+id/rl_icon_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        </com.ybmmarket20.view.TagView>
    </LinearLayout>

    <ImageView
        android:id="@+id/home_time_bg"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_gravity="center"
        android:layout_marginBottom="38dp"
        android:src="@drawable/home_time_bg"
        android:visibility="invisible" />

    <ImageView
        android:id="@+id/iv_tag_left"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_marginLeft="0.5dp"
        android:layout_marginTop="2dp"
        android:scaleType="fitXY" />

</FrameLayout>