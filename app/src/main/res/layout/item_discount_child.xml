<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_child_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dimen_dp_15"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="通用券 满300减200" />


    <TextView
        android:id="@+id/tv_coupon_used_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dimen_dp_3"
        android:paddingLeft="4dp"
        android:paddingRight="4dp"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_11"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/tv_child_title"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="已领取" />

    <TextView
        android:id="@+id/tv_child_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dimen_dp_12"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="-¥70.00" />


</androidx.constraintlayout.widget.ConstraintLayout>