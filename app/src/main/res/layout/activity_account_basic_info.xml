<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <!--        <include layout="@layout/common_header_items" />-->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/header_height_padding_top">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:padding="10dp"
            android:src="@drawable/ic_back" />

        <com.flyco.tablayout.SlidingTabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_centerHorizontal="true"
            android:background="@color/white"
            android:layout_marginStart="@dimen/dimen_dp_60"
            android:layout_marginEnd="@dimen/dimen_dp_60"
            app:tl_indicator_color="@color/color_theme_base_color"
            app:tl_indicator_corner_radius="2dp"
            app:tl_indicator_height="3dp"
            app:tl_indicator_margin_bottom="0dp"
            app:tl_indicator_width="34dp"
            app:tl_indicator_width_equal_title="false"
            app:tl_tab_padding="10dp"
            app:tl_tab_space_equal="true"
            app:tl_textBold="SELECT"
            app:tl_textSelectColor="@color/color_text_base_color"
            app:tl_textSelectSize="17dp"
            app:tl_textUnselectColor="@color/text_676773"
            app:tl_textsize="15dp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_F5F5F5" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />


</LinearLayout>
