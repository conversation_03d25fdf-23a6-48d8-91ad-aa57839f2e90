<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:elevation="0dp">

            <!--筛选-->
            <LinearLayout
                android:id="@+id/ll_filter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                app:layout_scrollFlags="scroll|enterAlways">

                <RadioGroup
                    android:id="@+id/rg_product_tab"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:background="@drawable/base_header_default_bg"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <FrameLayout
                        android:id="@+id/fl_all_product"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1">

                        <RadioButton
                            android:id="@+id/rb_all_product"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:button="@null"
                            android:checked="false"
                            android:clickable="false"
                            android:gravity="center"
                            android:text="全部商品"
                            android:textColor="@color/text_676773"
                            android:textSize="15sp" />

                        <View
                            android:layout_width="60dp"
                            android:layout_height="2dp"
                            android:layout_gravity="center_horizontal|bottom" />
                    </FrameLayout>

                    <View
                        android:layout_width="0.5dp"
                        android:layout_height="match_parent"
                        android:layout_marginBottom="2dp"
                        android:background="#33eeeeee" />

                    <FrameLayout
                        android:id="@+id/fl_sale_product"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1">

                        <RadioButton
                            android:id="@+id/rb_sale_product"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:button="@null"
                            android:checked="true"
                            android:clickable="false"
                            android:drawableBottom="@drawable/divider_line_2dp"
                            android:gravity="center"
                            android:text="只看有货商品"
                            android:textColor="@color/text_292933"
                            android:textSize="17sp" />

                        <View
                            style="@style/plan_tab_line"
                            android:layout_width="51dp" />

                    </FrameLayout>
                </RadioGroup>

                <RadioGroup
                    android:id="@+id/rb_filter"
                    android:layout_width="match_parent"
                    android:layout_height="42dp"
                    android:background="@color/white"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/rb_all"
                        style="@style/drug_list_tv_06"
                        android:layout_weight="1.2"
                        android:drawablePadding="0dp"
                        android:drawableRight="@null"
                        android:maxLength="6"
                        android:paddingLeft="0dp"
                        android:paddingRight="-5dp"
                        android:text="全部有货商品" />

                    <TextView
                        android:id="@+id/rb_all_category"
                        style="@style/drug_list_tv_06"
                        android:text="全部分类" />

                    <TextView
                        android:id="@+id/rb_comprehensive_ranking"
                        style="@style/drug_list_tv_06"
                        android:maxLength="4"
                        android:text="综合排序" />

                    <TextView
                        android:id="@+id/tv_shop"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginBottom="4dp"
                        android:layout_marginTop="4dp"
                        android:layout_weight="0.7"
                        android:button="@null"
                        android:drawablePadding="-5dp"
                        android:drawableRight="@drawable/icon_plan_screen_new"
                        android:gravity="center"
                        android:paddingRight="15dp"
                        android:text="@string/brand_manufacturers_filtrate"
                        android:textColor="@drawable/product_rb_selector_textcolor"
                        android:textSize="@dimen/brand_rb_01" />

                </RadioGroup>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/colors_f5f5f5" />
            </LinearLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/crv_product"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <RelativeLayout
        android:id="@+id/rl_bottom_add_cart"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/base_header_default_bg2">

        <CheckBox
            android:id="@+id/cb_select"
            style="@style/CustomCheckboxTheme"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:checked="true"
            android:clickable="false" />

        <TextView
            android:id="@+id/tv_select_all"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_marginLeft="5dp"
            android:layout_toRightOf="@+id/cb_select"
            android:gravity="center"
            android:text="全选"
            android:textColor="@color/color_292933"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_product_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="6dp"
            android:layout_toLeftOf="@+id/tv_add_purchase_order"
            android:text="已选择200个"
            android:textColor="@color/color_292933"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_add_purchase_order"
            android:layout_width="132dp"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:background="@color/base_colors_new"
            android:gravity="center"
            android:paddingLeft="6dp"
            android:paddingRight="6dp"
            android:text="加入购物车"
            android:textColor="#ffffff"
            android:textSize="16sp"
            android:textStyle="bold" />
    </RelativeLayout>
</LinearLayout>