<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_F7F7F7"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/header"
        layout="@layout/common_header_items" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_order_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:layout_marginTop="@dimen/dimen_dp_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header">

        <TextView
            android:id="@+id/tv_order_no_title"
            style="@style/InvoicePopTitle"
            android:text="订单编号:"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_order_no"
            style="@style/InvoicePopText"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:layout_constraintStart_toEndOf="@+id/tv_order_no_title"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="ybm10018028080808" />

        <View
            android:id="@+id/tv_order_no_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:background="@color/color_f7f7f8"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_no_title" />

        <TextView
            android:id="@+id/tv_order_time_title"
            style="@style/InvoicePopTitle"
            android:text="下单时间:"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_no_line" />

        <TextView
            android:id="@+id/tv_order_time"
            style="@style/InvoicePopText"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:layout_constraintStart_toEndOf="@+id/tv_order_time_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_no_line"
            tools:text="2021/03/12 11:23:34" />

        <View
            android:id="@+id/tv_order_time_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:background="@color/color_f7f7f8"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_time_title" />

        <TextView
            android:id="@+id/tv_receive_time_title"
            style="@style/InvoicePopTitle"
            android:text="支付时间:"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_time_line" />

        <TextView
            android:id="@+id/tv_receive_time"
            style="@style/InvoicePopText"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:layout_constraintStart_toEndOf="@+id/tv_receive_time_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_time_line"
            tools:text="2021/03/12 11:23:34" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/tv_receive_time_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_10"
        android:background="@color/color_f7f7f8"
        app:layout_constraintTop_toBottomOf="@+id/cl_order_info" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_invoice_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_receive_time_line">

        <TextView
            android:id="@+id/tv_type_title"
            style="@style/InvoicePopTitle"
            android:text="发票类型:"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_type"
            style="@style/InvoicePopText"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:layout_constraintStart_toEndOf="@+id/tv_type_title"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="电子普通发票" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <View
        android:id="@+id/tv_type_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/color_f7f7f8"
        app:layout_constraintTop_toBottomOf="@+id/cl_invoice_type" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:background="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_type_line"
        app:layout_constraintBottom_toTopOf="@+id/clAptitude"/>

    <include
        android:id="@+id/empty"
        layout="@layout/layout_empty_view"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dimen_dp_2"
        android:visibility="gone"
        android:layout_width="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_order_info"
        app:layout_constraintVertical_weight="1"
        app:layout_constraintBottom_toTopOf="@+id/clAptitude"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clAptitude"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_64"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtvAptitude"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_44"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:textSize="@dimen/dimen_dp_16"
            android:textColor="@color/white"
            android:text="申请发票售后"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_backgroundColor="@color/color_00b955"
            app:rv_cornerRadius="@dimen/dimen_dp_2" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>