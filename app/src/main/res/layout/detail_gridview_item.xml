<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_cart_section_content_01"
    android:foreground="?android:attr/selectableItemBackground"
    android:paddingLeft="6dp"
    android:paddingRight="6dp"
    android:paddingBottom="@dimen/dimen_dp_10">


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon"
        android:layout_width="144dp"
        android:layout_height="144dp"
        app:layout_constraintBottom_toBottomOf="@+id/iv_shop_mark"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_shop_mark" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_shop_mark"
        android:layout_width="match_parent"
        android:layout_height="182dp"
        android:layout_marginTop="6dp"
        android:scaleType="fitStart"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@tools:sample/backgrounds/scenic" />

    <com.ybmmarket20.view.PromotionTagView
        android:id="@+id/view_ptv"
        android:layout_width="144dp"
        android:layout_height="144dp"
        android:layout_centerInParent="true"
        app:contentTextSize="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/iv_shop_mark"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_shop_mark"
        app:shopBottomTextSize="8dp"
        app:shopContentTextSize="10dp"
        app:shopTimeTextSize="9dp"
        app:shopTopTextSize="9dp"
        app:subTitleTextSize="4dp"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_activity_price"
        style="@style/activity_price"
        android:layout_height="wrap_content"
        android:layout_marginLeft="45dp"
        android:textSize="11sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/iv_shop_mark"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:text="dsfjnfdsfdsf"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/shop_no_limit_tv01"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:background="@drawable/shop_limit01"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="12dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/iv_shop_mark"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_shop_mark"
        tools:text="售完"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2.5dp"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_shop_mark">

        <TextView
            android:id="@+id/shop_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/brand_shop_name"
            android:textSize="15sp"
            tools:text="士大夫" />

    </LinearLayout>

    <!-- 厂-->
    <LinearLayout
        android:id="@+id/ll_factory"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/ll_title">

        <com.ybmmarket20.common.widget.RoundTextView
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:gravity="center"
            android:includeFontPadding="true"
            android:text="厂"
            android:textColor="@color/tag_head_text"
            android:textSize="10sp"
            app:rv_backgroundColor="@color/tag_head_background"
            app:rv_cornerRadius="1dp"
            app:rv_strokeColor="@color/tag_head_stroke"
            app:rv_strokeWidth="1dp" />

        <TextView
            android:id="@+id/tv_chang_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5.5dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:lines="1"
            android:textColor="#939393"
            android:textSize="12sp"
            tools:text="广州白云山医药集团股份" />
    </LinearLayout>

    <!--效-->
    <LinearLayout
        android:id="@+id/ll_validity_period"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:orientation="horizontal"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/ll_factory">

        <com.ybmmarket20.common.widget.RoundTextView
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:gravity="center"
            android:includeFontPadding="true"
            android:text="效"
            android:textColor="@color/tag_head_text"
            android:textSize="10sp"
            app:rv_backgroundColor="@color/tag_head_background"
            app:rv_cornerRadius="1dp"
            app:rv_strokeColor="@color/tag_head_stroke"
            app:rv_strokeWidth="1dp" />

        <TextView
            android:id="@+id/tv_validity_period"
            style="@style/goods_list_item_text_small"
            android:layout_marginLeft="4dp"
            tools:text="xxxxxxxx" />
    </LinearLayout>


    <!-- 自营 -->
    <LinearLayout
        android:id="@+id/ll_company_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/ll_validity_period">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/icon_cart_proprietary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="4dp"
            android:gravity="center"
            android:includeFontPadding="true"
            android:padding="1.5dp"
            android:paddingLeft="3dp"
            android:paddingTop="1.5dp"
            android:paddingRight="3dp"
            android:text="自营"
            android:textColor="#00B377"
            android:textSize="12sp"
            android:visibility="visible"
            app:rv_backgroundColor="#0D00B377"
            app:rv_cornerRadius="1.33dp"
            app:rv_strokeColor="#8000B377"
            app:rv_strokeWidth="0.5dp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/icon_gross_margin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="true"
            android:padding="1.5dp"
            android:paddingLeft="3dp"
            android:paddingTop="1.5dp"
            android:paddingRight="3dp"
            android:text="高毛"
            android:textColor="@color/gross_margin_color"
            android:textSize="12sp"
            android:visibility="visible"
            app:rv_cornerRadius="1.33dp"
            app:rv_strokeColor="@color/gross_margin_color"
            app:rv_strokeWidth="0.5dp" />

        <TextView
            android:id="@+id/tv_company_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5.5dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#939393"
            android:textSize="12sp"
            tools:text="重庆小药药医药分公司" />

    </LinearLayout>

    <!-- 价格 -->
    <RelativeLayout
        android:id="@+id/rl_price"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        app:layout_constraintTop_toBottomOf="@+id/ll_company_name">

        <TextView
            android:id="@+id/shop_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/record_red"
            android:textSize="15dp"
            android:visibility="visible"
            tools:text="¥50" />

        <com.ybmmarket20.view.UnderlineTextView
            android:id="@+id/tv_original_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/shop_price"
            android:layout_marginLeft="3.5dp"
            android:layout_marginBottom="1.5dp"
            android:layout_toRightOf="@id/shop_price"
            android:ellipsize="end"
            android:lines="1"
            android:text="¥50"
            android:textColor="@color/text_9494A6"
            android:textSize="9dp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_oem"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:ellipsize="end"
            android:lines="1"
            android:text="价格签署协议可见"
            android:textColor="@color/brand_new_color"
            android:textSize="13sp"
            android:visibility="gone"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/tv_brand_control"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="暂无购买权限"
            android:textColor="@color/brand_control"
            android:textSize="17sp"
            android:visibility="gone"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/tv_audit_passed_visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/detail_tv_FF982C"
            android:textSize="@dimen/dimen_dp_14"
            android:visibility="gone"
            tools:text="价格认证资质可见"
            tools:visibility="gone" />
    </RelativeLayout>


    <!--购买按钮-->
    <com.ybmmarket20.view.ProductEditLayout
        android:id="@+id/el_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/rl_price"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/rl_price"
        tools:visibility="visible" />

    <!--中包装-->
    <TextView
        android:id="@+id/shop_price_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:ellipsize="end"
        android:lines="1"
        android:text="中包装：10盒"
        android:textColor="@color/brand_description_tv1"
        android:textSize="8.5dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/el_edit"
        app:layout_constraintRight_toRightOf="parent" />

    <!-- 到货提醒-->
    <ImageView
        android:id="@+id/iv_remind"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|right"
        android:background="@drawable/selector_remind_src"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rl_price"
        tools:visibility="gone" />

    <!--控销价/零售价 && 毛利率-->
    <TextView
        android:id="@+id/tv_retail_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        tools:text="零售价 ¥19.00（毛利率25%）"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_11"
        android:ellipsize="end"
        android:lines="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rl_price" />

    <!-- 促销标签-->
    <com.ybmmarket20.view.TagView
        android:id="@+id/rl_icon_type"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_retail_price" />

    <!-- 供应商（和自营 显示 是互斥的） -->
    <LinearLayout
        android:id="@+id/ll_gong"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/rl_icon_type">

        <TextView
            android:id="@+id/tv_open"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/icon_item_shop"
            android:drawableRight="@drawable/icon_open_shop"
            android:drawablePadding="2dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:lines="1"
            android:textColor="#939393"
            android:textSize="12sp"
            tools:text="九州通医药有限公司" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>