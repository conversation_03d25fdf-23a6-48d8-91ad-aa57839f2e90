<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_44"
    android:background="@color/white"
    app:layout_constraintTop_toBottomOf="@+id/header">

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="@dimen/dimen_dp_20"
        android:layout_height="@dimen/dimen_dp_20"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:src="@drawable/icon_my_wealth_bank_card"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_14"
        android:textColor="@color/color_333"
        android:text="银行卡"
        android:layout_marginStart="@dimen/dimen_dp_36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="@dimen/dimen_dp_5"
        android:layout_height="@dimen/dimen_dp_10"
        android:src="@drawable/icon_register_shop_address_arrow_right"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvDes"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_dp_30"
        android:textSize="@dimen/dimen_dp_14"
        android:textColor="#9494A6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="绑卡支付更便捷 | 去绑定" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0_5"
        android:background="#F5F5F5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>