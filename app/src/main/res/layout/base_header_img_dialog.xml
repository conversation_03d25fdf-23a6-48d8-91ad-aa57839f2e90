<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="270dp"
    android:layout_height="wrap_content"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="2dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.ybmmarket20.common.widget.RoundedImageView
            android:id="@+id/header_img"
            android:layout_width="match_parent"
            android:layout_height="113dp"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:riv_corner_radius_top_left="2dp"
            app:riv_corner_radius_top_right="2dp"
            tools:src="@tools:sample/avatars"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/header_img"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="24dp"
            android:letterSpacing="0.05"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:textColor="@color/color_292933"
            android:textSize="14sp"
            android:visibility="gone"
            tools:text="TitleTitleTitleTitleTitleTitle"
            tools:visibility="visible" />

        <!--如果为一行的话手动改marginTop 的值-->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_title"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:textColor="@color/color_676773"
            android:textSize="14sp"
            tools:text="DescDescDescDescDescDescDescDescDescDescDesc" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_desc"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:orientation="horizontal"
            android:paddingLeft="23dp"
            android:paddingRight="23dp"
            android:paddingBottom="20dp">

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/btn_cancel"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_marginRight="15dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="取消"
                android:textColor="@color/color_676773"
                android:textSize="14sp"
                android:visibility="visible"
                app:rv_cornerRadius="2dp"
                app:rv_strokeColor="@color/color_9494A6"
                app:rv_strokeWidth="1dp" />

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/btn_confirm"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="确认"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:rv_backgroundColor="@color/color_00B377"
                app:rv_cornerRadius="2dp" />

        </LinearLayout>
    </RelativeLayout>

</com.ybmmarket20.common.widget.RoundFrameLayout>