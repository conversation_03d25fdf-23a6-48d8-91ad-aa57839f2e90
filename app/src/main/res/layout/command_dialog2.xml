<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/dialog_bg"
        android:paddingLeft="25dp"
        android:paddingRight="25dp">

        <TextView
            android:id="@+id/tv_title"
            style="@style/command_dialog2_style"
            android:ellipsize="end"
            android:includeFontPadding="false"/>

        <TextView
            android:id="@+id/tv_content"
            style="@style/command_dialog2_style2"
            android:layout_below="@+id/tv_title"/>

        <TextView
            android:id="@+id/tv_content2"
            style="@style/command_dialog2_style2"
            android:layout_below="@+id/tv_content"
            android:textColor="@color/text_plan_999"/>

        <EditText
            android:id="@+id/tv_num"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_below="@+id/tv_content2"
            android:layout_marginTop="30dp"
            android:background="@drawable/bg_gv"
            android:gravity="center_vertical"
            android:hint="登记数量1"
            android:inputType="number"
            android:maxLength="7"
            android:paddingLeft="5dp"
            android:textColor="@color/text_plan_03"
            android:textCursorDrawable="@drawable/color_cursor"
            android:textSize="@dimen/text_plan_02"/>

        <EditText
            android:id="@+id/tv_price"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_below="@+id/tv_num"
            android:layout_marginTop="8dp"
            android:background="@drawable/bg_gv"
            android:gravity="center_vertical"
            android:hint="历史购进价"
            android:numeric="decimal"
            android:maxLength="7"
            android:paddingLeft="5dp"
            android:textColor="@color/text_plan_03"
            android:textCursorDrawable="@drawable/color_cursor"
            android:textSize="@dimen/text_plan_02"/>

        <LinearLayout
            android:id="@+id/ll_btn"
            android:layout_width="166dp"
            android:layout_height="50dp"
            android:layout_below="@+id/tv_price"
            android:layout_centerInParent="true"
            android:layout_marginTop="28dp"
            android:background="@drawable/line_bg_top_radius"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/btn_cancel"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginTop="0.4dp"
                android:layout_weight="1"
                android:background="@drawable/common_btn_gray_color_left_bg"
                android:gravity="center"
                android:text="取消"
                android:textColor="@color/text_plan_01"
                android:textSize="14sp"
                android:visibility="gone"/>

            <TextView
                android:id="@+id/btn_ok"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:background="@drawable/capture_btn_color_right_bg"
                android:gravity="center"
                android:text="添加到计划单"
                android:textColor="@color/white"
                android:textSize="16sp"/>
        </LinearLayout>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_btn"
            android:layout_centerInParent="true"
            android:layout_marginBottom="19dp"
            android:layout_marginTop="19dp">

            <TextView
                android:id="@+id/tv_add_capture"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="添加至"
                android:textColor="@color/text_plan_333"
                android:textSize="14sp"/>

            <TextView
                android:id="@+id/tv_change"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_margin="10dp"
                android:layout_toRightOf="@+id/tv_add_capture"
                android:text="更改"
                android:textColor="@color/base_color"
                android:textSize="14sp"/>
        </RelativeLayout>
    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_off"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentRight="true"
        android:src="@drawable/icon_capture_close"/>

</RelativeLayout>