<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_12corner_ffffff"
        android:padding="20dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/pax_core_dp_5"
            android:src="@drawable/close_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_success"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/icon_remind_success"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="23dp"
            android:text="提醒发货成功"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_success" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginTop="14dp"
            android:layout_marginEnd="18dp"
            android:gravity="center"
            android:textColor="#666666"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:text="提醒发货成功" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="35dp"
            android:layout_marginBottom="15dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_content">

            <TextView
                android:id="@+id/tv_go_line"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/shape_6corner_1900b955"
                android:drawableStart="@drawable/icon_call_service1"
                android:drawablePadding="6dp"
                android:gravity="center_vertical"
                android:paddingStart="55dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="联系商家"
                android:textColor="#00b955"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="11dp"
                android:layout_weight="1"
                android:background="@drawable/shape_6corner_00b955"
                android:gravity="center"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="我知道了"
                android:textColor="#ffffff"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>