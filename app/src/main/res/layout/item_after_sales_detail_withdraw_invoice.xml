<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dimen_dp_10"
    android:paddingStart="@dimen/dimen_dp_10"
    android:paddingEnd="@dimen/dimen_dp_10"
    android:orientation="vertical"
    android:background="@color/white">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:id="@+id/rllTips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:rv_backgroundColor="@color/color_f7f7f8"
        app:rv_cornerRadius="@dimen/dimen_dp_2">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请及时确认发票已退回"
            android:textSize="@dimen/dimen_dp_13"
            android:textColor="@color/color_292933"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_10" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="若您超时未处理，您的售后申请将自动关闭"
            android:textSize="@dimen/dimen_dp_13"
            android:textColor="@color/color_ff2121"
            android:layout_marginBottom="@dimen/dimen_dp_10"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_6" />
    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:id="@+id/rclInvoiceReturn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="@dimen/dimen_dp_10"
        app:rv_backgroundColor="#FFF6DF"
        app:rv_cornerRadius="@dimen/dimen_dp_2">

        <com.ybmmarket20.view.textview.RequiredTitleTextView
            android:id="@+id/rttvLogisticsTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="物流公司"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:textSize="@dimen/dimen_dp_14"
            android:textColor="@color/color_292933"
            android:layout_marginTop="@dimen/dimen_dp_12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/etLogistics"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_43"
            android:background="@color/transparent"
            android:hint="输入物流公司"
            android:textColorHint="#9494A6"
            android:gravity="end|center_vertical"
            android:textSize="@dimen/dimen_dp_14"
            android:maxLength="20"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/rttvLogisticsTitle" />

        <View
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_0_5"
            android:background="#CECECE"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/etLogistics" />

        <com.ybmmarket20.view.textview.RequiredTitleTextView
            android:id="@+id/rttvExpressTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="快递单号"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:textSize="@dimen/dimen_dp_14"
            android:textColor="@color/color_292933"
            android:layout_marginTop="@dimen/dimen_dp_12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/etLogistics" />

        <EditText
            android:id="@+id/etExpress"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_43"
            android:background="@color/transparent"
            android:hint="输入快递单号"
            android:textColorHint="#9494A6"
            android:gravity="end|center_vertical"
            android:textSize="@dimen/dimen_dp_14"
            android:maxLength="20"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/rttvExpressTitle"
            app:layout_constraintTop_toBottomOf="@+id/etLogistics" />

        <View
            android:id="@+id/divider"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_0_5"
            android:background="#CECECE"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/etExpress" />

        <TextView
            android:id="@+id/tvUploadImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="上传凭证"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:textSize="@dimen/dimen_dp_14"
            android:textColor="@color/color_292933"
            android:layout_marginTop="@dimen/dimen_dp_12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="(选填，最多3张)"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/dimen_dp_12"
            android:layout_marginStart="@dimen/dimen_dp_5"
            app:layout_constraintBottom_toBottomOf="@+id/tvUploadImage"
            app:layout_constraintStart_toEndOf="@+id/tvUploadImage"
            app:layout_constraintTop_toTopOf="@+id/tvUploadImage" />

        <FrameLayout
            android:id="@+id/flUploadImageReplace"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/tvUploadImage"
            tools:layout_height="@dimen/dimen_dp_100" />

    </com.ybmmarket20.common.widget.RoundConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:orientation="horizontal">

        <View
            android:id="@+id/placeHolder"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_0"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:visibility="gone"
            android:layout_weight="1" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtvWithdrawAfterSalesApplication"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_30"
            android:layout_weight="1"
            android:text="撤回售后申请"
            android:gravity="center"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_12"
            app:rv_cornerRadius="@dimen/dimen_dp_2"
            app:rv_strokeColor="@color/color_ccc"
            app:rv_strokeWidth="@dimen/dimen_dp_1" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtvEnsureInvoiceReturn"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_30"
            android:layout_weight="1"
            android:text="确认发票已退回"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_12"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:rv_backgroundColor="@color/color_00b377"
            app:rv_cornerRadius="@dimen/dimen_dp_2" />
    </LinearLayout>

</LinearLayout>