<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivOftenBuyEmpty"
        android:layout_width="@dimen/dimen_dp_200"
        android:layout_height="@dimen/dimen_dp_200"
        android:src="@drawable/icon_empty"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="没有找到相关商品"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintBottom_toBottomOf="@+id/ivOftenBuyEmpty" />

</androidx.constraintlayout.widget.ConstraintLayout>