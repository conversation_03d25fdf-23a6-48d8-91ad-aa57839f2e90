<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_bg"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_seckill_bg"
        android:scaleType="centerCrop"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@id/cl_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_35"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_title"
            android:scaleType="fitStart"
            android:layout_width="@dimen/dimen_dp_70"
            android:layout_height="@dimen/dimen_dp_18"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/icon_seckill_new" />

        <ImageView
            android:id="@+id/iv_des"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_17"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:scaleType="fitStart"
            android:layout_marginEnd="@dimen/dimen_dp_96"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/iv_title"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/llTime"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_17"
            android:orientation="horizontal"
            android:layout_marginStart="@dimen/dimen_dp_7"
            android:visibility="gone"
            android:gravity="center_vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_title"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvTimeTitle"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingStart="@dimen/dimen_dp_3"
                android:paddingEnd="@dimen/dimen_dp_3"
                android:gravity="center"
                android:textSize="@dimen/dimen_dp_10"
                tools:text="24点场" />

            <TextView
                android:id="@+id/tvTimeRemains"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:singleLine="true"
                android:layout_marginLeft="@dimen/dimen_dp_6"
                android:layout_marginRight="@dimen/dimen_dp_6"
                android:textSize="@dimen/dimen_dp_10"
                tools:text="02:24:25后开始" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_seckill_entry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:layout_marginEnd="@dimen/dimen_dp_3"
            android:textColor="@color/color_676773"
            android:drawableEnd="@drawable/icon_home_arrow_entry"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.526"
            tools:text="即将开抢" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_seckill"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_166"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingTop="@dimen/dimen_dp_4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_title"
        tools:itemCount="10"
        tools:listitem="@layout/item_seckill_goods" />

    <View
        android:id="@+id/v_bottom_corner"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rv_seckill" />

</androidx.constraintlayout.widget.ConstraintLayout>