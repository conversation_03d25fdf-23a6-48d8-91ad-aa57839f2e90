<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_f7f7f8"
    android:orientation="vertical">

    <!-- (不包括线下支付 线下支付会用下面的ConstraintLayout样式) 支付或提交成功后的头部信息；包括 支付方式、支付金额、查看订单、返回首页等  -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_normal_pay_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dimen_dp_10"
        android:background="@drawable/arl_fff_ball_corner_2"
        android:orientation="vertical"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_pay_result_logo"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginTop="@dimen/dimen_dp_28"
            android:background="@drawable/icon_payresult"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_pay_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:gravity="center_vertical"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_22"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_pay_result_logo"
            tools:text="支付成功" />

        <TextView
            android:id="@+id/tvDiscount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="银行卡支付，本次订单已优惠￥5.00"
            android:textSize="@dimen/dimen_dp_12"
            android:textColor="#FF7200"
            android:layout_marginTop="@dimen/dimen_dp_2"
            android:gravity="center"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintTop_toBottomOf="@+id/tv_pay_status"/>

        <TextView
            android:id="@+id/tv_pay_status_describe"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_60"
            android:layout_marginTop="11dp"
            android:layout_marginRight="@dimen/dimen_dp_60"
            android:gravity="center_horizontal"
            android:lineSpacingExtra="@dimen/dimen_dp_5"
            android:text="我们将尽快为您配送，您可进入我的订单查看订单配送信息。"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/dimen_dp_13"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvDiscount" />

        <TextView
            android:id="@+id/tv_payway_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_24"
            android:layout_weight="1"
            android:text="支付方式"
            android:textColor="@color/text_676773"
            android:textSize="@dimen/dimen_dp_13"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_pay_status_describe" />

        <TextView
            android:id="@+id/tv_payway"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:gravity="center_vertical"
            android:textColor="@color/color_292933"
            android:textSize="15sp"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_payway_text"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="支付宝" />

        <TextView
            android:id="@+id/tv_total_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_8"
            android:layout_weight="1"
            android:text="支付金额"
            android:textColor="@color/text_676773"
            android:textSize="@dimen/dimen_dp_13"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_payway_text" />

        <TextView
            android:id="@+id/tv_total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:gravity="center_vertical"
            android:textColor="@color/color_FF2121"
            android:textSize="15sp"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_total_text"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="¥14.26" />


        <!-- 开户信息-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_15"
            android:orientation="vertical"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_total_text">

            <LinearLayout
                android:id="@+id/ll_open_account"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">


                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/colors_fff7ef"
                    android:gravity="center_vertical"
                    android:minHeight="35dp"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="您在以下商户中未开户，请阅读开户流程，提前准备好资料"
                    android:textColor="@color/colors_99664D"
                    android:textSize="@dimen/dimen_dp_12" />

                <LinearLayout
                    android:id="@+id/ll_electronic_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <TextView
                        android:id="@+id/tv_name_01"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="广州医药集团有限公司提醒您："
                        android:textColor="@color/text_111334"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="@dimen/dimen_dp_20"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:lines="1"
                            android:singleLine="true"
                            android:text="1.证件扫描或拍照上传开户"
                            android:textColor="@color/color_676773"
                            android:textSize="@dimen/dimen_dp_12" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableRight="@drawable/icon_register_shop_address_arrow_right"
                            android:drawablePadding="4dp"
                            android:gravity="center_vertical"
                            android:text="查看开户流程"
                            android:textColor="@color/color_00b377"
                            android:textSize="12dp" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_pager_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <TextView
                        android:id="@+id/tv_name_02"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="13dp"
                        android:text="广州医药集团有限公司提醒您："
                        android:textColor="@color/text_111334"
                        android:textSize="14dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="@dimen/dimen_dp_20"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:lines="1"
                            android:singleLine="true"
                            android:textColor="@color/color_676773"
                            android:textSize="@dimen/dimen_dp_12"
                            tools:text="1.准备好证件复印件盖章邮寄，收到纸质版开户，收到纸质版开户，收到纸质版开户" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableRight="@drawable/icon_register_shop_address_arrow_right"
                            android:drawablePadding="4dp"
                            android:gravity="center_vertical"
                            android:text="查看开户流程"
                            android:textColor="@color/color_00b377"
                            android:textSize="12dp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_24"
                android:layout_marginBottom="@dimen/dimen_dp_20"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_detail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/order_green_border"
                    android:paddingLeft="32dp"
                    android:paddingTop="9dp"
                    android:paddingRight="32dp"
                    android:paddingBottom="9dp"
                    android:text="查看订单"
                    android:textColor="@color/color_00b377"
                    android:textSize="14sp" />

                <com.ybmmarket20.common.widget.RoundTextView
                    android:id="@+id/btn_home"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="26dp"
                    android:background="@drawable/order_green_border"
                    android:paddingLeft="32dp"
                    android:paddingTop="9dp"
                    android:paddingRight="32dp"
                    android:paddingBottom="9dp"
                    android:text="返回首页"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    app:rv_backgroundColor="@color/base_colors"
                    app:rv_cornerRadius="2dp" />


            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--线下支付-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_offline_pay_header"
        android:layout_width="match_parent"
        android:paddingBottom="20dp"
        android:layout_margin="10dp"
        android:visibility="gone"
        android:background="@drawable/arl_fff_ball_corner_2"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_offline_commit_success"
            android:layout_width="@dimen/dimen_dp_17"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="17dp"
            app:layout_constraintEnd_toStartOf="@id/tv_offline_pay_content"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@drawable/icon_offline_commit_success"
            android:layout_marginEnd="6dp"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:layout_height="@dimen/dimen_dp_17"/>

        <TextView
            android:id="@+id/tv_offline_pay_content"
            android:layout_width="wrap_content"
            tools:text="订单提交成功"
            app:layout_constraintTop_toTopOf="@id/iv_offline_commit_success"
            app:layout_constraintBottom_toBottomOf="@id/iv_offline_commit_success"
            android:textColor="@color/color_292933"
            android:textSize="14dp"
            app:layout_constraintStart_toEndOf="@id/iv_offline_commit_success"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_offline_pay_amount_content"
            android:layout_width="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_offline_pay_content"
            android:textSize="20dp"
            android:layout_marginTop="15dp"
            android:textColor="@color/color_292933"
            android:textStyle="bold"
            tools:text="应付金额 ¥1850.50"
            android:layout_height="wrap_content"/>


        <TextView
            android:id="@+id/tv_offline_pay_view_order"
            android:layout_width="0dp"
            android:text="查看订单"
            android:textColor="@color/color_00b955"
            android:textSize="14dp"
            android:gravity="center"
            android:layout_marginStart="20dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:layout_marginEnd="15dp"
            android:layout_marginTop="22dp"
            app:layout_constraintTop_toBottomOf="@id/tv_offline_pay_amount_content"
            app:layout_constraintEnd_toStartOf="@id/tv_offline_pay_back_home"
            android:background="@drawable/shape_2dp_white_cacaca"
            android:layout_height="32dp"/>

        <TextView
            android:id="@+id/tv_offline_pay_back_home"
            android:layout_width="0dp"
            android:text="返回首页"
            android:textColor="@color/color_00b955"
            android:textSize="14dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintTop_toTopOf="@id/tv_offline_pay_view_order"
            app:layout_constraintStart_toEndOf="@id/tv_offline_pay_view_order"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@drawable/shape_2dp_white_cacaca"
            android:layout_height="32dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 转账信息 + 转账说明 -->
    <LinearLayout
        android:id="@+id/ll_bank_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:visibility="visible"
        android:background="@color/color_f7f7f8"
        android:orientation="vertical">

        <!-- 转账信息 -->
        <com.ybmmarket20.common.widget.RoundLinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:orientation="vertical"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="@dimen/dimen_dp_2">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_top_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="6dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:gravity="center_vertical"
                    android:text="转账信息"
                    android:textColor="@color/text_292933"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:id="@+id/ll_copy"
                    android:layout_width="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tv_title"
                    android:paddingHorizontal="10dp"
                    app:layout_constraintBottom_toBottomOf="@id/tv_title"
                    android:paddingVertical="3dp"
                    android:layout_height="26dp">

                    <ImageView
                        android:id="@+id/iv_offline_copy"
                        android:layout_width="12dp"
                        android:layout_gravity="center"
                        android:layout_marginEnd="4dp"
                        android:src="@drawable/icon_offline_copy"
                        android:layout_height="12dp"/>

                    <TextView
                        android:id="@+id/tv_copy"
                        android:text="复制"
                        android:textSize="13dp"
                        android:layout_gravity="center"
                        android:textColor="@color/color_00b377"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:minHeight="40dp"
                android:paddingLeft="10dp"
                android:paddingRight="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="公司名称"
                    android:textColor="@color/text_9494A6"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_bank_info_01"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="27dp"
                    android:text=""
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:minHeight="40dp"
                android:paddingLeft="10dp"
                android:paddingRight="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="开户银行"
                    android:textColor="@color/text_9494A6"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_bank_info_02"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="27dp"
                    android:text=""
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:minHeight="40dp"
                android:paddingLeft="10dp"
                android:paddingRight="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="银行账户"
                    android:textColor="@color/text_9494A6"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_bank_info_03"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="27dp"
                    android:text=""
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_bank_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="3dp"
                android:paddingLeft="20dp"
                android:paddingTop="3dp"
                android:paddingRight="20dp"
                android:paddingBottom="8dp"
                android:textColor="@color/text_676773"
                android:textSize="14sp"
                android:visibility="gone"
                tools:text="公司名称：xxx 开户银行：xxx 银行账户：xxx xxxx xxxx xxxx"
                tools:visibility="visible" />
        </com.ybmmarket20.common.widget.RoundLinearLayout>

        <!--上传电汇凭证-->
        <com.ybmmarket20.common.widget.RoundLinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dimen_dp_10"
            android:orientation="vertical"
            android:padding="@dimen/dimen_dp_10"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="@dimen/dimen_dp_2">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/tv_upload_voucher"
                    android:layout_width="wrap_content"
                    android:textStyle="bold"
                    android:textColor="@color/color_292933"
                    android:textSize="15dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:text="上传电汇凭证"
                    android:layout_height="wrap_content"/>

                <ImageView
                    android:id="@+id/iv_view_examples"
                    android:layout_width="14dp"
                    app:layout_constraintEnd_toStartOf="@id/tv_view_examples"
                    app:layout_constraintTop_toTopOf="@id/tv_view_examples"
                    android:src="@drawable/icon_view_examples"
                    app:layout_constraintBottom_toBottomOf="@id/tv_view_examples"
                    android:layout_height="14dp"/>
                
                <TextView
                    android:id="@+id/tv_view_examples"
                    android:layout_width="wrap_content"
                    android:textColor="@color/color_00b377"
                    android:text="查看示例"
                    android:textSize="13dp"
                    app:layout_constraintTop_toTopOf="@id/tv_upload_voucher"
                    app:layout_constraintBottom_toBottomOf="@id/tv_upload_voucher"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:paddingEnd="10dp"
                    android:gravity="center"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/tv_upload_tips"
                    android:layout_width="0dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_upload_voucher"
                    android:layout_marginTop="13dp"
                    android:text="*您的线下转账订单需要上传电汇凭证，为不影响发货效率，请您尽快上传（最多可上传三张）"
                    android:textSize="12dp"
                    android:textColor="@color/text_color_999999"
                    android:layout_height="wrap_content"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_upload_voucher"
                    android:layout_width="0dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_upload_tips"
                    android:layout_marginTop="15dp"
                    android:layout_height="wrap_content"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.ybmmarket20.common.widget.RoundLinearLayout>

        <!-- 转账说明 -->
        <com.ybmmarket20.common.widget.RoundLinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dimen_dp_10"
            android:orientation="vertical"
            android:padding="@dimen/dimen_dp_10"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="@dimen/dimen_dp_2">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:text="转账说明"
                android:textColor="@color/text_292933"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="30dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="10dp"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="4dp"
                        android:layout_height="4dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="12dp"
                        android:src="@drawable/bg_bank_tips" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_bank_tips_head"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:lineSpacingExtra="3dp"
                    android:paddingTop="3dp"
                    android:text="请您在汇款时备注药帮忙订单编号，这将会很大程度上缩短我们的核款时间并能尽快为您安排发货。"
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:minHeight="30dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="10dp"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="4dp"
                        android:layout_height="4dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="12dp"
                        android:src="@drawable/bg_bank_tips" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_bank_tips1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:lineSpacingExtra="3dp"
                    android:paddingTop="3dp"
                    android:text=""
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

            </LinearLayout>
            <TextView
                android:id="@+id/tv_pay_result_check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="6dp"
                android:drawableRight="@drawable/icon_pay_result_check"
                android:drawablePadding="5dp"
                android:text="查看操作示例"
                android:textColor="@color/base_colors"
                android:textSize="15sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:minHeight="30dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="10dp"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="4dp"
                        android:layout_height="4dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="12dp"
                        android:src="@drawable/bg_bank_tips" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_bank_tips2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:lineSpacingExtra="3dp"
                    android:paddingTop="3dp"
                    android:text=""
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:minHeight="30dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="10dp"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="4dp"
                        android:layout_height="4dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="12dp"
                        android:src="@drawable/bg_bank_tips" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_bank_tips3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:lineSpacingExtra="3dp"
                    android:paddingTop="3dp"
                    android:text=""
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

            </LinearLayout>

        </com.ybmmarket20.common.widget.RoundLinearLayout>


    </LinearLayout>

    <TextView
        android:id="@+id/tv_action"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:minHeight="40dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:visibility="gone"
        tools:text="线下转账相关信息"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_kefu"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_margin="@dimen/dimen_dp_10"
        android:background="@color/white">

        <TextView
            android:id="@+id/ll_kefu_tv1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/icon_phone"
            android:drawablePadding="10dp"
            android:gravity="center_vertical"
            android:text="若有疑问，请联系客服 400-0505-111"
            android:textColor="#292933"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_third_company_kefu"
        android:layout_width="match_parent"
        android:layout_height="51dp"
        android:layout_margin="@dimen/dimen_dp_10"
        android:background="@color/white">

        <ImageView
            android:id="@+id/iv_third_company_kefu"
            android:src="@drawable/icon_third_company_kefu"
            android:layout_width="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_third_company_kefu"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:layout_marginTop="19dp"
            android:layout_marginEnd="6dp"
            android:layout_height="16dp"/>

        <TextView
            android:id="@+id/tv_third_company_kefu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="联系商家在线客服"
            android:textColor="@color/text_color_333333"
            android:textSize="14dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_third_company_kefu"
            app:layout_constraintEnd_toEndOf="parent"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_pop_kefu_all"
        android:layout_width="match_parent"
        android:layout_height="51dp"
        android:layout_margin="@dimen/dimen_dp_10"
        tools:visibility="visible"
        android:visibility="gone"
        android:background="@color/white">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_line"
            app:layout_constraintGuide_percent="0.5"
            android:layout_width="wrap_content"
            android:orientation="vertical"
            android:layout_height="wrap_content"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_pop_kefu"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="@id/guide_line"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_height="0dp">
            
            <ImageView
                android:id="@+id/iv_pop_kefu"
                android:layout_width="18dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:src="@drawable/icon_order_kefu"
                android:layout_marginStart="30dp"
                app:layout_constraintEnd_toStartOf="@id/tv_pop_kefu"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginEnd="5dp"
                android:layout_height="18dp"/>

            <TextView
                android:id="@+id/tv_pop_kefu"
                android:layout_width="wrap_content"
                android:textSize="14dp"
                android:text="联系客服"
                android:textColor="@color/color_676773"
                android:paddingTop="5dp"
                android:paddingEnd="35dp"
                android:layout_marginBottom="6dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_pop_kefu"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_faster_processing"
                android:layout_width="37dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                app:layout_constraintTop_toTopOf="@id/tv_pop_kefu"
                app:layout_constraintEnd_toEndOf="@id/tv_pop_kefu"
                android:text="处理更快"
                android:textColor="@color/white"
                android:background="@drawable/shape_faster_processing"
                android:textSize="8dp" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_call_phone"
            android:layout_width="0dp"
            app:layout_constraintStart_toEndOf="@id/guide_line"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_height="0dp">
            
            
            <ImageView
                android:id="@+id/iv_call_phone"
                android:src="@drawable/icon_order_phone"
                android:layout_width="18dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_call_phone"
                app:layout_constraintHorizontal_chainStyle="packed"
                android:layout_marginEnd="5dp"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_height="18dp"/>

            <TextView
                android:id="@+id/tv_call_phone"
                android:layout_width="wrap_content"
                android:textSize="14dp"
                android:text="拨打电话"
                android:textColor="@color/color_676773"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_call_phone"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/v_divider"
            android:layout_width="1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@color/colors_f5f5f5"
            android:layout_height="20dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>