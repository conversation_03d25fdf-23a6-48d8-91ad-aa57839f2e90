<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="@dimen/dimen_dp_40"
    android:layout_width="match_parent"
    android:layout_marginStart="@dimen/dimen_dp_10"
    android:layout_marginEnd="@dimen/dimen_dp_10"
    android:background="@color/white">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="搭配套餐："
        android:layout_marginStart="@dimen/dimen_dp_9"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_9494A6"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintStart_toEndOf="@+id/tv_title" />

</androidx.constraintlayout.widget.ConstraintLayout>