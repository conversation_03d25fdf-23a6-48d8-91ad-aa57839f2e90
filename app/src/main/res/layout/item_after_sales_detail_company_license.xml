<?xml version="1.0" encoding="utf-8"?>
<com.google.android.flexbox.FlexboxLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/dimen_dp_10"
    android:paddingEnd="@dimen/dimen_dp_10"
    android:background="@color/white"
    app:flexWrap="wrap"
    tools:layout_height="@dimen/dimen_dp_50" />