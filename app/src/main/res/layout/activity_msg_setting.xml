<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg"
    android:divider="@drawable/divider_line_w_1px"
    android:orientation="vertical"
    android:showDividers="middle">

    <include layout="@layout/common_header_items" />

    <RelativeLayout
        android:id="@+id/ll_msg"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:background="@color/white"
        android:minHeight="50dp"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:id="@+id/tv"
            style="@style/about_layout_tv"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginTop="5dp"
            android:text="活动通知" />

        <TextView
            style="@style/about_layout_tv"
            android:layout_below="@id/tv"
            android:layout_marginTop="2dp"
            android:text="有活动消息时，在通知栏提示"
            android:textColor="@color/text_9494A6"
            android:layout_marginBottom="3dp"
            android:textSize="14dp" />

        <CheckBox
            android:id="@+id/cb_setting"
            style="@style/addressCheckboxTheme"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:checked="true"
            android:clickable="false" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/btn"
        android:layout_width="170dp"
        android:layout_height="45dp"
        android:layout_gravity="center"
        android:layout_marginBottom="20dp"
        android:background="@drawable/order_gray_border"
        android:gravity="center"
        android:visibility="gone"
        android:text="我收不到优惠通知"
        android:textColor="@color/color_292933"
        android:textSize="14dp"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="2dp"
        app:rv_strokeColor="@color/colors_E4E4EB"
        app:rv_strokeWidth="0.5dp"  />
</LinearLayout>