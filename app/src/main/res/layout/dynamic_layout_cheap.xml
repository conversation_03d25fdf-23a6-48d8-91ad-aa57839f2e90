<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:gravity="center"
        android:text="动态布局标题"
        android:textSize="16sp" />
    <!--增加自己的固定布局-->
    <com.ybmmarket20.view.HorizontalScrollViewLoadMore
        android:id="@+id/sl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        android:overScrollMode="never"
        android:fillViewport="true">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <com.ybmmarket20.view.MyGridView
                android:id="@+id/cheap_gv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:listSelector="@null"
                android:scrollbars="none"
                android:layout_weight="1"
                android:stretchMode="columnWidth"
                />
            <ImageView
                android:id="@+id/iv_more"
                android:layout_width="27dp"
                android:src="@drawable/load_more"
                android:layout_height="match_parent" />
        </LinearLayout>
    </com.ybmmarket20.view.HorizontalScrollViewLoadMore>
</merge>