package com.ybmmarket20.report.coupon

import android.text.TextUtils
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.view.homesteady.whenAllNotNull

class CouponUtil {

    companion object {

        /**
         * 给路由添加spId
         */
        fun wrapperRouterUrlWithParam(routerUrl: String?, spId: String?): String {
            return whenAllNotNull(routerUrl, spId) { url, p ->
                return@whenAllNotNull if (url.contains("?")) "$url&spId=$p"
                else "$url?spId=$p"
            }?: ""
        }

        /**
         * 给路由添加spId 、商品skuId、shopName 和 shopCode
         */
        fun wrapperRouterUrlWithParam(routerUrl: String?, spId: String?, rowsBean: RowsBean?): String {
            var url = whenAllNotNull(routerUrl, spId) { url, p ->
                return@whenAllNotNull if (url.contains("?")) "$url&spId=$p"
                else "$url?spId=$p"
            }?: ""
            if (url.isNotEmpty()) {
                url = "$url&skuId=${rowsBean?.id?: ""}&shopName=${rowsBean?.shopName?: ""}&shopCode=${rowsBean?.shopCode?: ""}"
            }
            return url
        }

        /**
         * 给路由添加spId 和 商品的 shopCode
         */
        fun wrapperRouterUrlWithParam(routerUrl: String?, param: String?, shopCode: String?, shopName: String): String {
            return whenAllNotNull(routerUrl, param) { url, p ->
                return@whenAllNotNull if (url.contains("?")) "$url&spId=$p&shopCode=$shopCode&shopName=$shopName"
                else "$url?spId=$p&shopCode=$shopCode&shopName=$shopName"
            }?: ""
        }
    }
}