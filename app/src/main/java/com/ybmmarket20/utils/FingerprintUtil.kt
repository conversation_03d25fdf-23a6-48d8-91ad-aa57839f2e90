package com.ybmmarket20.utils

import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import java.util.concurrent.Executor

/**
 * 指纹工具
 */
class FingerprintUtil {

    companion object {

        /**
         * 是否支持指纹
         */
        fun checkFingerprintSupported(context: Context): <PERSON><PERSON>an {
            return BiometricManager.from(context).canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG) == BiometricManager.BIOMETRIC_SUCCESS
        }

        /**
         * 验证指纹
         */
        fun fingerprintAuthenticate(fragmentActivity: FragmentActivity, title: String, subTitle: String, negativeButtonText: String, callback: FingerprintCallback) {
            val promptInfo = BiometricPrompt.PromptInfo.Builder()
                .setTitle(title)
                .setSubtitle(subTitle)
                .setNegativeButtonText(negativeButtonText)
                .build()
            getBiometricPrompt(fragmentActivity, callback).authenticate(promptInfo)
        }

        private fun getBiometricPrompt(fragmentActivity: FragmentActivity, callback: FingerprintCallback): BiometricPrompt {
            return BiometricPrompt(fragmentActivity, FingerprintMainExecutor(), object :
                BiometricPrompt.AuthenticationCallback() {

                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    super.onAuthenticationSucceeded(result)
                    callback.onSuccess()
                }

                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    super.onAuthenticationError(errorCode, errString)
                    callback.onFail(errorCode, errString)
                }

                override fun onAuthenticationFailed() {
                    super.onAuthenticationFailed()
                    callback.onFail(Integer.MIN_VALUE, "失败")
                }
            })
        }


        private fun getBiometricPromptByFragment(fragment: Fragment, callback: FingerprintCallback): BiometricPrompt {
            return BiometricPrompt(fragment, FingerprintMainExecutor(), object :
                BiometricPrompt.AuthenticationCallback() {

                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    super.onAuthenticationSucceeded(result)
                    callback.onSuccess()
                }

                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    super.onAuthenticationError(errorCode, errString)
                    callback.onFail(errorCode, errString)
                }

                override fun onAuthenticationFailed() {
                    super.onAuthenticationFailed()
                    callback.onFail(Integer.MIN_VALUE, "失败")
                }
            })
        }


    }

    class FingerprintMainExecutor: Executor {

        private val handler: Handler = Handler(Looper.getMainLooper())

        override fun execute(p0: Runnable?) {
            p0?.let { handler.post(it) }
        }

    }

    interface FingerprintCallback {
        fun onSuccess()

        fun onFail(errorCode: Int, errString: CharSequence)
    }
}