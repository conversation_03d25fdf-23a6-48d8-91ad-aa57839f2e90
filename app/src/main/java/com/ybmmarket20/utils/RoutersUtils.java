package com.ybmmarket20.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;

import androidx.activity.ComponentActivity;
import androidx.lifecycle.SavedStateViewModelFactory;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.savedstate.SavedStateRegistryOwner;

import android.text.TextUtils;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.request.FutureTarget;
import com.bumptech.glide.request.target.Target;
import com.github.mzule.activityrouter.router.Routers;
import com.google.gson.Gson;
import com.xyy.app.rsa.Base64;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.FileStorageUtil;
import com.ybm.app.utils.IntentUtil;
import com.ybmmarket20.activity.LoginActivity;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.SpellGroupRecommendGoodsBean;
import com.ybmmarket20.common.APPUpdateManager;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.constant.RouterConstantKt;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.view.YbmCommandPopWindow;
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel;
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore;
import com.ybmmarket20.xyyreport.spm.RouterHelper;

import java.io.File;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 路由分发，打开相关页面与执行相关指令
 */
public class RoutersUtils {
    public static final String MATCHPATTERN = "((http[s]{0,1}|ftp)://.{1,})";
    public static final String PAGE = "ybmpage";
    public static final String PAGE_NEW = "ybm100";
    public static final String ACTION = "ybmaction";
    public static final String API = "ybmapi";

    //自定义指令
    private static final String EXIT = "exit";//退出app
    private static final String FINISH = "finish";//关闭当前页面
    private static final String SHOW = "show";
    private static final String SHARE = "share";//分享功能
    private static final String HIDE = "hide";
    private static final String SAVEPHOTO = "savephoto";//保存图片
    private static final String SHOW_DEF = "showdef";
    private static final String SHOW_ICON = "showicon";
    private static final String SHOW_ALL = "showall";
    private static final String LOGOUT = "logout";
    private static final String UPDATE = "update";//更新app
    private static final String PATCH = "patch";//补丁更新app
    private static final String REBOOT = "reboot";
    private static final String SCROLL = "scroll";
    private static final String SP = "sp";//更新sp
    private static final String LOCALBROADCAST = "localbroadcast";//本地广播
    private static final String TEL = "tel";//电话
    private static final String KEFU = "kefu";//客服
    private static final String SMS = "sms";//短信
    private static final String QQ = "qq";//打开qq聊天
    private static final String WX = "wx";//打开微信
    private static final String INTENT = "intent";//打开intent
    private static final String ACTIVITY = "activity";//打开intent
    private static final String CLEAR_SUI_XIN_PIN_DATA = "clearsuixinpindata";//清除随心拼数据

    public static final String API_HOST_RELEASE = "app-v4.ybm100.com";//
    public static final String API_HOST_STAGE = "new-app.stage.ybm100.com";//
    public static final String API_HOST_TEST = "new-app.test.ybm100.com";//

    public static final String ROUTER_HOST_CMS_RELEASE = "app.ybm100.com";
    public static final String ROUTER_HOST_CMS_STAGE = "app.stage.ybm100.com";
    public static final String ROUTER_HOST_CMS_TEST = "app.test.ybm100.com";

    //客服电话
    public final static String DEFOULT_KEFUPHONE = "400-0505-111";
    public static String kefuPhone = DEFOULT_KEFUPHONE;
    public final static String YKQ_DEFOULT_KEFUPHONE = "400-0507-788";
    public static String ykqkefuPhone = YKQ_DEFOULT_KEFUPHONE;
    private static final String SPLIT = ",";
    public static boolean isClickable = true;//已经下架的商品是否可以点击进入商品详情页面。

    //初始化,加快第一次点击跳转的速度
    public static void init() {
        try {
            open("test://host/");
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public static String paramsToBase64(String params) {
        if (TextUtils.isEmpty(params)) return "";
        return android.util.Base64.encodeToString(params.getBytes(), android.util.Base64.URL_SAFE);
    }

    public static String getStringFromBase64(String base64Params) {
        if (TextUtils.isEmpty(base64Params)) return base64Params;
        return new String(android.util.Base64.decode(base64Params, android.util.Base64.URL_SAFE));
    }

    public static boolean open(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        //映射URL 因为有些url变动 兼容后端情况
        url = urlMapping(url);

        if (patternString(url)) {
            return true;
        }
        return open(YBMAppLike.getApp().getCurrActivity(), url);
    }

    //url映射转换
    private static String urlMapping(String url){
        String mUrl = url;
        //订单路由更换到首页
        if (url.contains("ybmpage://myorderlist")){
            mUrl = "ybmpage://main/3";
        }
        return mUrl;
    }

//    public static boolean open(String url, String spmCnt, String scmCnt) {
//        if (TextUtils.isEmpty(url)) {
//            return false;
//        }
//        if (patternString(url)) {
//            return true;
//        }
//        String resultUrl = RouterHelper.makeupUrlForSpm(YBMAppLike.getApp().getCurrActivity(), url, spmCnt, scmCnt);
//        return open(YBMAppLike.getApp().getCurrActivity(), resultUrl);
//    }

    public static <T> String getBase64BeanToString(T value) {
        try {
            Gson gson = new Gson();
            String originalStr = gson.toJson(value);
            return android.util.Base64.encodeToString(originalStr.getBytes(StandardCharsets.UTF_8), android.util.Base64.URL_SAFE);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static <T> T getBase64StringToBean(String value, Class<T> clazz) {
        try {
            Gson gson = new Gson();
            String base64String = new String(android.util.Base64.decode(value, android.util.Base64.URL_SAFE));
            return gson.fromJson(base64String, clazz);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // 只支持单一action
    public static boolean openForResult(String url, int requestCode) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        if (patternString(url)) {
            return false;
        }
        Uri uri = Uri.parse(url);
        if (uri == null) {
            return false;
        }
        String scheme = uri.getScheme();
        if (ACTION.equals(scheme)) {
            return handlerAction(uri);
        } else if (API.equals(scheme)) {
            return handlerApi(uri);
        }
        return Routers.openForResult(YBMAppLike.getApp().getCurrActivity(), uri, requestCode);
    }
    private static List<String> reParseUri(String url) {
        if (TextUtils.isEmpty(url)) return null;
        List<String> uriList = new ArrayList<>();
        if (url.contains(SPLIT)) {
            String[] actions = url.split(SPLIT);
            StringBuilder routerUri = new StringBuilder();
            for (String action : actions) {
                Uri uri = Uri.parse(action);
                String scheme = uri.getScheme();
                if (scheme == null && !TextUtils.isEmpty(routerUri.toString())) {
                    routerUri.append(",");
                } else if (scheme != null){
                    uriList.add(routerUri.toString());
                    routerUri = new StringBuilder();
                }
                routerUri.append(action);
            }
            if (!TextUtils.isEmpty(routerUri.toString())) {
                uriList.add(routerUri.toString());
            }
        } else uriList.add(url);
        return uriList;
    }

    //支持多个action 同时运行
    private static boolean open(Context context, String url) {
        List<String> urls = reParseUri(url);
        if (urls == null) return false;
        boolean result = false;
        for (String action : urls) {
            if (!TextUtils.isEmpty(action)) {
                result = open(context, Uri.parse(action));
            }
        }
        return result;
    }

    private static boolean open(Context context, Uri uri) {
        if (uri == null) {
            return false;
        }
        if (context == null) {
            context = BaseYBMApp.getAppContext();
        }
        String scheme = uri.getScheme();
        if (ACTION.equals(scheme)) {
            return handlerAction(uri);
        } else if (API.equals(scheme)) {
            return handlerApi(uri);
        }
        return Routers.open(context, uri);
    }

    /**
     * 路由向下走之前优先走一下http https ftp 等通用路由
     *
     * @param url
     * @return
     */
    private static boolean patternString(String url) {

        boolean matches = Pattern.matches(MATCHPATTERN, url);
        LogUtils.tag("router").e("是否匹配： " + matches);

        if (Pattern.matches(MATCHPATTERN, url)) {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            if (intent != null) {
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                BaseYBMApp.getAppContext().startActivity(intent);
                return true;
            }
            return true;
        } else {
            return false;
        }

    }


    public static boolean appScheme(String uri) {
        if (uri == null) {
            return false;
        }
        return uri.startsWith(API) || uri.startsWith(ACTION) || uri.startsWith(PAGE) || uri.startsWith(PAGE_NEW);
    }


    private static boolean handlerApi(Uri uri) {
        String host = uri.getHost();
        if (!TextUtils.isEmpty(host) && host.equals("host")) {
            String url = "";
            if (uri.getPath() != null) {
                url = uri.getPath();
            }
            if (uri.getEncodedQuery() != null) {
                url += "?" + uri.getEncodedQuery();
            }
            LogUtils.d(url);
            HttpManager.getInstance().postParser(url, new BaseResponse<EmptyBean>() {

                @Override
                public void onSuccess(String content, BaseBean<EmptyBean> data, EmptyBean obj) {

                }
            });
            return true;
        } else {
            HttpManager.getInstance().postParser(uri.toString().replaceFirst(API, "http"), new BaseResponse<EmptyBean>() {

                @Override
                public void onSuccess(String content, BaseBean<EmptyBean> data, EmptyBean obj) {

                }
            });
            return true;
        }
    }

    //处理自己定义指令
    private static boolean handlerAction(Uri uri) {
        String host = uri.getHost();
        if (TextUtils.isEmpty(host)) {
            return false;
        } else {
            return handlerHostAction(host, uri);
        }
    }

    //处理没有参数的host
    private static boolean handlerHostAction(String host, Uri uri) {
        if (host.equals(FINISH)) {//关闭当前页面
            if (YBMAppLike.getApp().getCurrActivity() != null) {
                YBMAppLike.getApp().getCurrActivity().finish();
            }
        } else if (host.equals(LOGOUT)) {//退出登陆
            return logoutApp();
        } else if (host.equals(SAVEPHOTO)) {//保存图片,缓存图片
            String name = uri.getQueryParameter("name");
            String show = uri.getQueryParameter("loading");
            String url = uri.toString();
            if (url.contains("&url=")) {
                url = url.substring(url.indexOf("&url=") + 5);
            }
            LogUtils.d("name:" + name + " url:" + url + " show:" + show);
            return saveImgUrl(url, name, (!TextUtils.isEmpty(show) && ("1".equals(show) || "true".equals(show))));
        } else if (host.equals(EXIT)) {//退出app
            return exitApp();

            //注意！！！   版本更新已经不走这里了，走ybmAppupdate这个sdk   MainActivity.checkUpdate()调用了 可看那里
        } else if (host.equals(UPDATE)) {//检查app有没有 更新
            String force = uri.getQueryParameter("force");
            String api = uri.getQueryParameter("api");
            if (TextUtils.isEmpty(force)) {
                return updateApp(false);
            } else {
                if (TextUtils.isEmpty(api) || api.length() <= 5) {
                    return forceUpdateApp();
                } else {
                    api = new String(com.xyy.app.rsa.Base64.decode(api));
                    if (api.startsWith("http") || api.startsWith("Http")) {
                        return forceUpdateApp(api);
                    } else {
                        return forceUpdateApp(AppNetConfig.HOST + api);
                    }
                }
            }
        } else if (host.equals(PATCH)) {//检查补丁更新
            String force = uri.getQueryParameter("force");
            return updateAppBuglyPatch(!TextUtils.isEmpty(force));
        } else if (host.equals(REBOOT)) {//重启app
            return rebootApp();
        } else if (host.equals(INTENT)) {//启动通用intent
            try {
                String str = uri.getQueryParameter("intent");
                if (TextUtils.isEmpty(str)) {
                    return false;
                }
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(str));
                if (intent != null) {
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    BaseYBMApp.getAppContext().startActivity(intent);
                    return true;
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
            return false;
        } else if (host.equals(ACTIVITY)) {//启动通用intent
            try {
                String str = uri.getQueryParameter("intent");
                if (TextUtils.isEmpty(str)) {
                    return false;
                }
                Intent intent = Intent.getIntent(str);
                if (intent != null) {
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                    return true;
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
            return false;
        } else if (host.equals(LOCALBROADCAST)) {//启动通用本地广播
            try {
                String str = uri.getQueryParameter("action");
                if (TextUtils.isEmpty(str)) {
                    return false;
                }
                Intent intent = new Intent(str);
                if (intent != null) {
                    LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(intent);
                    return true;
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
            return false;
        } else if (host.equals(TEL)) {
            String num = uri.getQueryParameter("num");
            String show = uri.getQueryParameter("show");
            if (TextUtils.isEmpty(num)) {
                return false;
            }
            return telPhone(!TextUtils.isEmpty(show), num);
        } else if (host.equals(KEFU)) {
            String show = uri.getQueryParameter("show");
            if (TextUtils.isEmpty(show)) {
                return telKefu(false);
            } else {
                return telKefu(true);
            }
        } else if (host.equals(QQ)) {
            String num = uri.getQueryParameter("num");
            if (TextUtils.isEmpty(num)) {
                return IntentUtil.openPackage(YBMAppLike.getAppContext(), "com.tencent.mobileqq");
            }
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("mqqwpa://im/chat?chat_type=wpa&uin=" + num));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            BaseYBMApp.getAppContext().startActivity(intent);
            return true;
        } else if (host.equals(WX)) {
            return IntentUtil.openPackage(YBMAppLike.getAppContext(), "com.tencent.mm");
        } else if (host.equals(SMS)) {
            String num = uri.getQueryParameter("num");
            if (TextUtils.isEmpty(num)) {
                num = "";
            }
            String content = uri.getQueryParameter("content");
            Intent intent = new Intent(Intent.ACTION_SENDTO, Uri.parse("smsto:" + num));
            if (!TextUtils.isEmpty(content)) {
                intent.putExtra("sms_body", content);
            }
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            BaseYBMApp.getAppContext().startActivity(intent);
            return true;
        } else if (host.equals(SCROLL)) {
            String position_str = uri.getQueryParameter("position");
            int position = 0;
            try {
                position = Integer.parseInt(position_str);
            } catch (Throwable e) {
                return false;
            }
            if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseActivity.Scroll) {
                ((BaseActivity.Scroll) BaseYBMApp.getApp().getCurrActivity()).scroll2Position(position);
                return true;
            }
            return false;
        } else if (host.equals(SP)) {
            String key = uri.getQueryParameter("sp_key");
            String value = uri.getQueryParameter("sp_value");
            if (TextUtils.isEmpty(key)) {
                return false;
            }
            try {
                SpUtil.writeString(key, value);
                SpUtil.writeInt(key, Integer.parseInt(value));//如果是数字
            } catch (Throwable e) {
                e.printStackTrace();
            }
            return true;
        } else if (host.equals(SHOW)) {
            return showMe();
        } else if (host.equals(HIDE)) {
            return hideMe();
        } else if (host.equals(SHOW_DEF)) {
            return SystemUtil.showDefIcon();
        } else if (host.equals(SHOW_ICON)) {
            return SystemUtil.showActivityIcon();
        } else if (host.equals(SHOW_ALL)) {
            return SystemUtil.showTwoIcon();
        } else if (host.equals(SHARE)) {//分享
            String key = uri.getQueryParameter("share_key");
            String str_type = uri.getQueryParameter("share_type");
            try {
                int type = Integer.parseInt(str_type);
                return share(key, type);
            } catch (Throwable e) {
                return false;
            }
        } else if (host.equals(CLEAR_SUI_XIN_PIN_DATA)) {
            //清除随心拼数据
            Activity activity = BaseYBMApp.getApp().getCurrActivity();
            if (!(activity instanceof ComponentActivity)) return false;
            SpellGroupRecommendGoodsViewModel viewModel = new ViewModelProvider(
                    GlobalViewModelStore.Companion.get().getGlobalViewModelStore(), new SavedStateViewModelFactory(
                    activity.getApplication(), (SavedStateRegistryOwner) activity))
                    .get(SpellGroupRecommendGoodsViewModel.class);
            SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean = viewModel.getSpellGroupRecommendGoodsLiveData().getValue();
            if (spellGroupRecommendGoodsBean == null
                    || spellGroupRecommendGoodsBean.getRowsBean() == null
                    || spellGroupRecommendGoodsBean.getRowsBean().isEmpty()
            ) {
                GlobalViewModelStore.Companion.get().getGlobalViewModelStore().clear();
                activity.finish();
                return false;
            }
            else {
                Map<String, Integer> mapping = spellGroupRecommendGoodsBean.getGoodsIdMapping();
                boolean isFinish = true;
                for (String key : mapping.keySet()) {
                    if (mapping.get(key) != 0) {
                        isFinish = false;

                    }
                }
                if (isFinish) {
                    activity.finish();
                    return false;
                }
            }
            AlertDialogEx dialogEx = new AlertDialogEx(BaseYBMApp.getApp().getCurrActivity());
            dialogEx.setMessage("返回将清空所有随心拼商品确定返回吗？").setCancelButton("取消", null)
                    .setConfirmButton("确定", (AlertDialogEx.OnClickListener) (dialog, button) -> {
                        GlobalViewModelStore.Companion.get().getGlobalViewModelStore().clear();
                        BaseYBMApp.getApp().getCurrActivity().finish();
                    }).show();
        } else {
            return false;
        }
        return true;
    }

    public static boolean telKefu(boolean showDialog, boolean showChannel) {
        if (BaseYBMApp.getApp().getCurrActivity() == null) {
            return false;
        }
        String kefu = showChannel ? ykqkefuPhone : kefuPhone;
        if (showDialog) {
            AlertDialogEx dialogEx = new AlertDialogEx(BaseYBMApp.getApp().getCurrActivity());
            dialogEx.setMessage("呼叫客服：" + kefu).setCancelButton("取消", null).setConfirmButton("呼叫", new AlertDialogEx.OnClickListener() {
                @Override
                public void onClick(AlertDialogEx dialog, int button) {
                    Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + kefu));
                    BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                }
            }).show();
        } else {
            Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + kefu));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
        }
        return true;
    }

    //拨打客服
    public static boolean telKefu(boolean showDialog) {
        if (BaseYBMApp.getApp().getCurrActivity() == null) {
            return false;
        }
        if (showDialog) {
            AlertDialogEx dialogEx = new AlertDialogEx(BaseYBMApp.getApp().getCurrActivity());
            dialogEx.setMessage("呼叫客服：" + kefuPhone).setCancelButton("取消", null).setConfirmButton("呼叫", new AlertDialogEx.OnClickListener() {
                @Override
                public void onClick(AlertDialogEx dialog, int button) {
                    Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + kefuPhone));
                    BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                }
            }).show();
        } else {
            Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + kefuPhone));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
        }
        return true;
    }

    //拨打客服
    public static boolean telKefu(boolean showDialog, String phoneNumber, String message) {
        if (BaseYBMApp.getApp().getCurrActivity() == null) {
            return false;
        }
        if (showDialog) {
            AlertDialogEx dialogEx = new AlertDialogEx(BaseYBMApp.getApp().getCurrActivity());
            dialogEx.setMessage(message + phoneNumber).setCancelButton("取消", null).setConfirmButton("呼叫", new AlertDialogEx.OnClickListener() {
                @Override
                public void onClick(AlertDialogEx dialog, int button) {
                    Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + phoneNumber));
                    BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                }
            }).show();
        } else {
            Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + phoneNumber));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
        }
        return true;
    }

    public static boolean telPhone(boolean showDialog, final String num, String numPrefix) {
        if (BaseYBMApp.getApp().getCurrActivity() == null) {
            return false;
        }
        if (showDialog) {
            AlertDialogEx dialogEx = new AlertDialogEx(BaseYBMApp.getApp().getCurrActivity());
            dialogEx.setMessage(numPrefix + num).setCancelButton("取消", null).setConfirmButton("呼叫", new AlertDialogEx.OnClickListener() {
                @Override
                public void onClick(AlertDialogEx dialog, int button) {
                    Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + num));
                    BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                }
            }).show();
        } else {
            Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + num));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
        }
        return true;
    }

    public static boolean telPhone(boolean showDialog, final String num) {
        return telPhone(showDialog, num, "呼叫：");
    }

    /**
     * @param url 图片下载地址
     * @return
     */
    public static boolean saveImgUrl(String url) {
        return saveImgUrl(url, null, true);
    }

    /**
     * @param url          图片下载地址
     * @param fileName     文件名不包含路径
     * @param showProgress 是否显示加载提示
     * @return
     */
    public static boolean saveImgUrl(String url, final String fileName, final boolean showProgress) {
        if (TextUtils.isEmpty(url)) {
            ToastUtils.showShort("下载地址是空的");
            return false;
        }
        if (!url.startsWith("http")) {
            url = AppNetConfig.getCDNHost() + url;
        }
        final String imgUrl = url;
        if (showProgress) {
            showProgress();
        }
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                try {
                    FutureTarget<File> future = ImageHelper.with(BaseYBMApp.getAppContext()).load(imgUrl).downloadOnly(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL);
                    File file = future.get();
                    // 首先保存图片
                    File pictureFolder = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).getAbsoluteFile();
                    File appDir = new File(pictureFolder, "YBM");
                    if (!appDir.exists()) {
                        appDir.mkdirs();
                    }
                    String name = fileName;
                    if (TextUtils.isEmpty(name)) {
                        name = System.currentTimeMillis() + ".png";
                    }
                    if (!name.endsWith(".png") && !name.endsWith(".jpg")) {
                        name = name + ".png";
                    }
                    final File destFile = new File(appDir, name);
                    if (destFile.exists()) {
                        try {
                            destFile.delete();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    FileStorageUtil.copyFile(file, destFile);
                    if (showProgress) {
                        dismissProgress();
                    }
                    // 最后通知图库更新
                    BaseYBMApp.getApp().sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(new File(destFile.getPath()))));
                    SmartExecutorManager.getInstance().executeUI(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtils.showShort("图片保存成功:" + destFile.getAbsolutePath(), true);
                            updateMedia(destFile.getAbsolutePath());
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    ToastUtils.showShort("图片下载出错");
                }
            }
        });
        return true;
    }

    public static void updateMedia(String... path) {
        MediaScannerConnection.scanFile(BaseYBMApp.getAppContext(), path, new String[]{"image/jpeg", "image/png"}, null);
    }


    public static boolean rebootApp() {
        Intent launchIntent = BaseYBMApp.getAppContext().getPackageManager().getLaunchIntentForPackage(BaseYBMApp.getAppContext().getPackageName());
        launchIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        BaseYBMApp.getAppContext().startActivity(launchIntent);
        System.exit(0);
        Process.killProcess(Process.myPid());
        return true;
    }

    public static boolean exitApp() {
        try {
            ((BaseActivity) (YBMAppLike.getApp().getCurrActivity())).finish();
            System.exit(0);
            Process.killProcess(Process.myPid());
            return true;
        } catch (Throwable e) {
            e.printStackTrace();
            System.exit(0);
            Process.killProcess(Process.myPid());
            return true;
        }
    }

    //退出app
    public static boolean logoutApp() {
        try {
            // 延迟清除 merchantid
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    SpUtil.clearMerchantid();
                }
            }, 300);//延迟300ms 防止正在请求的接口正准备拿merchantid 拿不到报错
            if (YBMAppLike.getApp().getCurrActivity() == null || !(YBMAppLike.getApp().getCurrActivity() instanceof LoginActivity)) {
                Intent intent = new Intent(YBMAppLike.getAppContext(), LoginActivity.class);
                if (YBMAppLike.getApp().getCurrActivity() != null) {
                    YBMAppLike.getApp().getCurrActivity().startActivity(intent);
                } else {
                    rebootApp();
                }
            }

        } catch (Throwable e) {
            LogUtils.d(e);
            rebootApp();
            SpUtil.clearMerchantid();
        }
        return true;
    }

    public static boolean updateApp(boolean showDialog) {
        new APPUpdateManager().checkUpdate(showDialog);
        return true;
    }

    public static boolean updateAppBugly(boolean showDialog) {
        return true;
    }

    public static boolean updateAppBuglyPatch(boolean reboot) {

        return true;
    }

    //强制升级到最新app,没有版本号
    public static boolean forceUpdateApp(String url) {
        new APPUpdateManager().forceUpdateApp(url, true);
        return true;
    }

    //强制升级到最新app,没有版本号
    public static boolean forceUpdateApp() {
        new APPUpdateManager().forceUpdateApp(AppNetConfig.getCDNHost() + "/ybm/download/ybm.apk", true);
        return true;
    }


    //解决 url中包含# 不能解析参数的bug
    public static String getParameter(String uri, String key) {
        if (TextUtils.isEmpty(uri) || TextUtils.isEmpty(key)) {
            return null;
        }
        if (!uri.contains(key)) {
            return null;
        }
        int index = uri.indexOf(key) + key.length() + 1;
        uri = uri.substring(index);
        index = uri.indexOf("&");
        if (index == 0) {
            return "";
        } else if (index < 0) {//ybmpage://commonh5activity?url=https://stage-app.ybm100.com/static/?ybm_title=先声药业#!harpro
            index = uri.indexOf("#!");
            if (index > 0) {
                return uri.substring(0, index);
            }
            return uri;
        } else {
            return uri.substring(0, index);
        }
    }

    public static String encodeRAWUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return url;
        }
        url = urlEncode(url);
        if (TextUtils.isEmpty(url)) {
            return url;
        }
        try {
            return Base64.encode(url.getBytes("UTF-8"));
        } catch (Throwable e) {

        }
        return "";
    }

    public static String decodeRAWUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return url;
        }
        try {
            url = new String(Base64.decode(url), "UTF-8");
        } catch (Throwable e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(url)) {
            return url;
        }
        return urlDecode(url);
    }

    public static String urlEncode(String url) {
        if (TextUtils.isEmpty(url)) {
            return url;
        }
        try {
            return URLEncoder.encode(url, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String urlDecode(String url) {
        if (TextUtils.isEmpty(url)) {
            return url;
        }
        try {
            return URLDecoder.decode(url, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static boolean showMe() {
        IntentUtil.openPackage(BaseYBMApp.getAppContext(), BaseYBMApp.getAppContext().getPackageName());
        return true;
    }

    public static boolean hideMe() {
        IntentUtil.openHome(BaseYBMApp.getAppContext());
        return true;
    }

    public static boolean share(String key, int type) {
        if (key == null) {
            return false;
        }
        if (type < 0) {
            return false;
        }
        new YbmCommandPopWindow().encodeCommand(key, type);
        return true;
    }

    public static boolean showProgress() {
        if (YBMAppLike.getApp().getCurrActivity() == null || !(YBMAppLike.getApp().getCurrActivity() instanceof BaseActivity)) {
            return false;
        }
        ((BaseActivity) YBMAppLike.getApp().getCurrActivity()).showProgress();
        return true;
    }

    public static boolean dismissProgress() {
        if (YBMAppLike.getApp().getCurrActivity() == null || !(YBMAppLike.getApp().getCurrActivity() instanceof BaseActivity)) {
            return false;
        }
        ((BaseActivity) YBMAppLike.getApp().getCurrActivity()).dismissProgress();
        return true;
    }

    public static void openOutBrowser(Context context, String url) {
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        Uri content_url = Uri.parse(url);
        intent.setData(content_url);
        context.startActivity(Intent.createChooser(intent, "请选择浏览器打开"));
    }

    /**
     *2021-01-12 更新
     * 1、增加入参：isThirdCompany 是否第三方厂家（0：否；1：是）
     * 2、跳转拼接参数：
     *     2.1、自营相关不变。
     *     2.2、POP相关拼接参数如下：
     *     userid 买家ID（merchantId）
     *     merchantCode 卖家ID（orgId）
     *     pop 固定为 1
     *     ws 固定为 1
     *    orderNo 订单编号
     *
     * @param im_pack_url
     * @param orgId
     * @param orderNo
     * @return
     */
    public static String getRouterPopCustomerServiceUrl(String im_pack_url,String orgId,
                                                        String orderNo,String merchantName){
        //联系商家在线客服，电汇商业
        return RouterConstantKt.ROUTER_COMMON_H5_ACTIVITY_CACHE_URL+im_pack_url + "&userid=" + SpUtil.getMerchantid() + "&sc=1003&portalType=1&merchantCode="+orgId + "&pop=1&ws=1"+((TextUtils.isEmpty(orderNo)?"":"&orderNo="+orderNo))+"&merchantName="+merchantName+"&pType=1";
    }
    public static String getRouterYbmDetailCustomerServiceUrl(String url){//商品详情页联系平台客服
        return RouterConstantKt.ROUTER_COMMON_H5_ACTIVITY_CACHE_URL+url + "&userid=" + SpUtil.getMerchantid() + "&sc=1003" + "&portalType=1" + "&pType=1";
    }
    public static String getRouterYbmOrderCustomerServiceUrl(String url,String orderNo){//订单详情页联系平台客服
        return RouterConstantKt.ROUTER_COMMON_H5_ACTIVITY_CACHE_URL+url + "&userid=" + SpUtil.getMerchantid() +"&orderNo=" + orderNo+ "&sc=1003" + "&portalType=2" + "&pType=1";
    }

    public static String getRouterYbmOrderCustomerServiceUrl(String url,String orgId,
                                                             String orderNo,String merchantName){//电汇平台客服
        return RouterConstantKt.ROUTER_COMMON_H5_ACTIVITY_CACHE_URL+url + "&userid=" + SpUtil.getMerchantid()+"&orderNo="+orderNo+"&merchantCode="+orgId+"&merchantName="+merchantName;
    }

}
