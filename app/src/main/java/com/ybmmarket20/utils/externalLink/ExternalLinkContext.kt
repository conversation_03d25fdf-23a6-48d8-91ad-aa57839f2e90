package com.ybmmarket20.utils.externalLink

import android.net.Uri
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.*
import com.ybmmarket20.utils.externalLink.impl.*
import java.net.URLDecoder

/**
 * 外部链接调起应用处理路由
 * <AUTHOR>
 */
class ExternalLinkContext(private val baseActivity: BaseActivity) {

    fun setUri(uri: Uri) {
        handleType(uri)
    }

    private fun handleType(uriFromH5: Uri?) {
        if (uriFromH5 == null) return
        val scheme: String = uriFromH5.scheme ?: ""
        val query: String = uriFromH5.query ?: ""
        val path: String = uriFromH5.path ?: ""
        val uri = URLDecoder.decode(uriFromH5.toString())
        when {
            scheme == ROUTER_SCHEME_YAOBANGMANG ->
                //兼容老版本scheme=yaobangmang
                CompatExternalLink(baseActivity)
            getSchemeYBM100Condition(scheme, !uri.contains(query)) ->
                //url中不包含query
                NoQueryExternalLink(baseActivity)
            getSchemeYBM100Condition(scheme, query.contains(ROUTER_SCHEME_HTTP)) ->
                //scheme=ybm100 query包含http
                QueryHttpExternalLink(baseActivity)
            getSchemeYBM100Condition(scheme, path == ROUTER_PATH_COMMON_H5) ->
                //跳转h5页面
                CommonH5ExternalLink(baseActivity)
            getSchemeYBM100Condition(scheme, path == ROUTER_PATH_REGISTER) ->
                //跳转注册
                RegisterExternalLink(baseActivity)
            getSchemeYBM100Condition(scheme, true) ->
                //scheme=ybm100，使用url的path为路由authority,query需要base64解码
                YBM100PathExternalLink(baseActivity)
            else -> DefaultExternalLink(baseActivity)
        }.handleUri(uriFromH5)
    }

    /**
     * 处理scheme=ybm100 判断条件
     */
    private fun getSchemeYBM100Condition(scheme: String, condition: Boolean): Boolean = scheme == ROUTER_SCHEME_YBM100 && condition

}