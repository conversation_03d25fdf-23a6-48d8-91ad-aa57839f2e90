package com.ybmmarket20.utils.im.core.callback

import com.ybmmarket20.utils.im.core.data.IMMessage
import com.ybmmarket20.utils.im.core.data.MemberInfo

/**
 * <AUTHOR>
 * IM回调
 */

interface IMCoreCallback {
    /**
     * 正在初始化
     */
    fun onInitConnecting()

    /**
     * 初始化成功
     */
    fun onInitConnectSuccess()

    /**
     * 初始化失败
     */
    fun onInitConnectFail(code: Int, msg: String?)

    /**
     * 反初始化
     */
    fun onUnInit()

    /**
     * 登录成功
     */
    fun onLoginSuccess()

    /**
     * 登录失败
     */
    fun onLoginFail(code: Int, msg: String?)

    /**
     * 登出成功
     */
    fun onLogoutSuccess()

    /**
     * 登出失败
     */
    fun onLogoutFail(code: Int, msg: String?)

    /**
     * 凭证过期
     */
    fun onUserSigExpired()

    /**
     * 用户信息变更
     */
    fun onSelfInfoUpdated(memberInfo: MemberInfo?)

    /**
     * 被踢下线
     */
    fun onKickedOffline()

    /**
     * 加入群组成功
     */
    fun onJoinGroupSuccess()

    /**
     * 加入群组失败
     */
    fun onJoinGroupFail(code: Int, msg: String?)

    /**
     * 退出群组成功
     */
    fun onQuiteGroupSuccess()

    /**
     * 退出群组失败
     */
    fun onQuiteGroupFail(code: Int, msg: String?)

    /**
     * 发送群文本消息成功
     */
    fun onSendGroupTextMessageSuccess(imMessage: IMMessage)

    /**
     * 发送群文本消息失败
     */
    fun onSendGroupTextMessageFail(code: Int, msg: String?)

    /**
     * 发送群自定义消息成功
     */
    fun onSendGroupCustomMessageSuccess(imMessage: IMMessage)

    /**
     * 发送群自定义消息失败
     */
    fun onSendGroupCustomMessageFail(code: Int, msg: String?)

    /**
     * 用户离开房间
     */
    fun onReceiveGroupMemberLeave(groupId: String?, member: MemberInfo?)

    /**
     * 用户进入房间
     */
    fun onReceiveGroupMemberEnter(groupId: String?, memberList: List<MemberInfo>?)

    /**
     * 接收系统消息
     */
    fun onReceiveCustomSystemMessage(groupId: String?, customData: ByteArray?)

}