package com.ybmmarket20.network.request

import com.ybmmarket20.bean.AptitudeXyyBean
import com.ybmmarket20.bean.BaseBean
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface QualificationRequest {

    @FormUrlEncoded
    @POST("shop/license")
    suspend fun getSelfQualification(@Field("merchantId")merchantId: String, @Field("shopCode")shopCode: String): BaseBean<AptitudeXyyBean>

    @FormUrlEncoded
    @POST("company/center/companyInfo/getCorporationQualification")
    suspend fun getPopQualification(@Field("orgId")orgId: String): BaseBean<AptitudeXyyBean>


}