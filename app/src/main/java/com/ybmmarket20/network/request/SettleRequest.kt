package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.SettleBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 订单
 */
interface ISettleService {
    @FormUrlEncoded
    @POST("order/v1/groupPurchasePreSettle")
    suspend fun preSettleSpellGroupTranNo(@Field("merchantId") merchantId: String, @Field("skuId") skuId: String, @Field("productNum") productNum: String): BaseBean<SettleBean>
}

class SettleRequest {

    /**
     * 拼团获取tranNo
     */
    suspend fun preSettleSpellGroupTranNo(merchantId: String, skuId: String, productNum: String): BaseBean<SettleBean> = try {
        NetworkService.instance.mRetrofit.create(ISettleService::class.java).preSettleSpellGroupTranNo(merchantId, skuId, productNum)
    } catch (e: Exception) {
        BaseBean<SettleBean>().initWithException(e)
    }
}