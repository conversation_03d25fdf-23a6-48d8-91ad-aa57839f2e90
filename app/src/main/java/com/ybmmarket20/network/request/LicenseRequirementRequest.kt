package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CompanyLicenseBean
import com.ybmmarket20.bean.aftersales.AfterSalesLicenseGoods
import com.ybmmarket20.bean.aftersales.AfterSalesLicenseGoodsTemp
import com.ybmmarket20.bean.aftersales.AfterSalesNoBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface ILicenseRequirementService {

    @FormUrlEncoded
    @POST("afterSales/queryCredentialType")
    suspend fun getCompanyLicenseList(@Field("merchantId") merchantId: String): BaseBean<CompanyLicenseBean>

    @FormUrlEncoded
    @POST("afterSales/queryCredentialType")
    suspend fun getCompanyLicenseListWithType(@Field("merchantId") merchantId: String, @Field("type")type: String): BaseBean<CompanyLicenseBean>

    @FormUrlEncoded
    @POST("afterSales/credentialApply")
    suspend fun submitLicenseAfterSales(@FieldMap paramsMap: Map<String, String>): BaseBean<AfterSalesNoBean>

    @FormUrlEncoded
    @POST("order/queryOrderDetailList")
    suspend fun getLicenseGoodsList(@Field("orderNo")orderNo: String): BaseBean<MutableList<AfterSalesLicenseGoodsTemp>>
}

class LicenseRequirementRequest {

    suspend fun getCompanyLicenseList(): BaseBean<CompanyLicenseBean> = try {
        NetworkService.instance.mRetrofit.create(ILicenseRequirementService::class.java).getCompanyLicenseList(SpUtil.getMerchantid())
    } catch (e: Exception) {
        BaseBean<CompanyLicenseBean>().initWithException(e)
    }

    suspend fun getCompanyLicenseListWithType(type: String): BaseBean<CompanyLicenseBean> = try {
        NetworkService.instance.mRetrofit.create(ILicenseRequirementService::class.java).getCompanyLicenseListWithType(SpUtil.getMerchantid(), type)
    } catch (e: Exception) {
        BaseBean<CompanyLicenseBean>().initWithException(e)
    }

    suspend fun submitLicenseAfterSales(paramsMap: Map<String, String>): BaseBean<AfterSalesNoBean> = try {
        NetworkService.instance.mRetrofit.create(ILicenseRequirementService::class.java).submitLicenseAfterSales(paramsMap)
    } catch (e: Exception) {
        BaseBean<AfterSalesNoBean>().initWithException(e)
    }

    suspend fun getLicenseGoodsList(@Field("orderNo")orderNo: String): BaseBean<MutableList<AfterSalesLicenseGoodsTemp>> = try {
        NetworkService.instance.mRetrofit.create(ILicenseRequirementService::class.java).getLicenseGoodsList(orderNo)
    } catch (e: Exception) {
        BaseBean<MutableList<AfterSalesLicenseGoodsTemp>>().initWithException(e)
    }

}