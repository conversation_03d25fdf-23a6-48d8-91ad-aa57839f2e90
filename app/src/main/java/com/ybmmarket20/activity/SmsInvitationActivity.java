package com.ybmmarket20.activity;

import android.content.Intent;
import android.database.Cursor;
import android.provider.ContactsContract;
import com.google.android.material.textfield.TextInputLayout;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.ImgCodeBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.ButtonObserver;
import com.ybmmarket20.view.CommonDialogLayout;
import com.ybmmarket20.view.EditText2WithDel;

import butterknife.Bind;
import butterknife.OnClick;

/*
 * 短信邀请好友
 * */
@Router("smsinvitation")
public class SmsInvitationActivity extends BaseActivity {

    private static final int SELECT_CONTACT = 101;
    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.tv_title)
    TextView tvTitle;
    @Bind(R.id.tv_right)
    TextView tvRight;
    @Bind(R.id.iv_right)
    ImageView ivRight;
    @Bind(R.id.ll_title)
    RelativeLayout llTitle;
    @Bind(R.id.sms_invitation_user_name_wrapper)
    TextInputLayout smsInvitationUserNameWrapper;
    @Bind(R.id.et_sms_invitation_user_name_wrapper)
    EditText2WithDel etSmsInvitationUserNameWrapper;
    @Bind(R.id.btn_ok)
    ButtonObserver btnOk;
    @Bind(R.id.sms_invitation_code)
    EditText smsInvitationCode;
    @Bind(R.id.sms_invitation_code_wrapper)
    TextInputLayout smsInvitationCodeWrapper;
    @Bind(R.id.sms_invitation_img_code)
    ImageView smsInvitationImgCode;

    private String imgCodeId;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_sms_invitation;
    }

    @Override
    protected void initData() {

        setTitle("短信邀请好友");

        btnOk.observer(smsInvitationCode);
        btnOk.setOnItemClickListener(new ButtonObserver.OnButtonObserverListener() {
            @Override
            public void onButtonObserver(boolean isFlag) {
                if (isFlag) {
                    setButtonStyle(R.drawable.sms_round_corner_bg, UiUtils.getColor(R.color.white));
                } else {
                    setButtonStyle(R.drawable.sms_round_corner_gray_bg, UiUtils.getColor(R.color.white));
                }
            }
        });

        etSmsInvitationUserNameWrapper.setmOnAddressBookListener(new EditText2WithDel.onAddressBookListener() {
            @Override
            public void onAddressBook(String text) {
                //从有电话号码的联系人中选取
                Intent jumpIntent = new Intent(Intent.ACTION_PICK);
                jumpIntent.setType(ContactsContract.CommonDataKinds.Phone.CONTENT_TYPE);
                startActivityForResult(jumpIntent, SELECT_CONTACT);
            }
        });

        getImgCode();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case SELECT_CONTACT:
                    //选择通讯录联系人返回
                    if (data == null) {
                        return;
                    }

                    try {
                        if (data.getData() != null) {

                            Cursor cursor = getContentResolver().query(data.getData(),
                                    new String[]{ContactsContract.CommonDataKinds.Phone.NUMBER
                                            , ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME},
                                    null, null, null);

                            while (cursor.moveToNext()) {
                                //取出该条数据的联系人姓名
                                String name = cursor.getString(1).replaceAll(" ", "");
                                //取出该条数据的联系人的手机号
                                String number = cursor.getString(0).replaceAll(" ", "").replaceAll("-", "");

                                if (number.length() > 11) {
                                    number = number.substring(number.length() - 11, number.length());
                                }
                                etSmsInvitationUserNameWrapper.setText(number);
                                etSmsInvitationUserNameWrapper.setSelection(number.length());
                            }
                            cursor.close();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 设置登录按钮风格
     *
     * @param drawableStyle 背景样式
     * @param colorStyle    文字颜色
     */
    public void setButtonStyle(int drawableStyle, int colorStyle) {
        if (btnOk == null) {
            return;
        }
        btnOk.setBackgroundResource(drawableStyle);
        btnOk.setTextColor(colorStyle);
    }

    @OnClick({R.id.btn_ok, R.id.sms_invitation_img_code})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_ok:
                hideSoftInput();
                sendSmsReq();
                break;
            case R.id.sms_invitation_img_code:
                //获取图片验证
                getImgCode();
                break;
        }
    }

    /***
     * 获取图片验证码
     * **/
    private void getImgCode() {
        smsInvitationImgCode.setEnabled(false);
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        HttpManager.getInstance().post(AppNetConfig.GET_VERIF_CODE, params, new BaseResponse<ImgCodeBean>() {
            @Override
            public void onSuccess(String content, BaseBean<ImgCodeBean> obj, ImgCodeBean data) {
                if (smsInvitationImgCode == null) {
                    return;
                }
                smsInvitationImgCode.setEnabled(true);
                if (obj != null && obj.isSuccess() && data != null) {
                    //重新加载imgCode
                    showImgCode(data.codeImg);
                    imgCodeId = data.code;
                } else {
                    showImgCode(null);
                }
            }

            public void onFailure(NetError error) {
                smsInvitationImgCode.setEnabled(true);
                showImgCode(null);
            }
        });
    }

    private void showImgCode(String url) {
        LogUtils.d(url);
        if (!TextUtils.isEmpty(url) && !url.startsWith("http")) {
            url = AppNetConfig.getCDNHost() + url;
        }
        ImageHelper.with(this).load(url).error(R.drawable.error_img).diskCacheStrategy(DiskCacheStrategy.NONE).into(smsInvitationImgCode);
    }

    /*
     * 点击发送短信邀请验证
     * */
    public void sendSmsReq() {

        String sms_phone = etSmsInvitationUserNameWrapper.getText().toString().trim();
        String sms_code = smsInvitationCode.getText().toString().trim();

        if (!UiUtils.isMobileNO(sms_phone)) {
            DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.failed, "请输入正确的手机号");
            etSmsInvitationUserNameWrapper.requestFocus();
            return;
        }
        if (TextUtils.isEmpty(sms_code) || sms_code.length() <= 2) {
            DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.failed, "请输入图形验证码");
            smsInvitationCode.requestFocus();
            return;
        }
        showProgress();
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("authCode", merchantId);
        params.put("mobileNumber", sms_phone);
        params.put("photoCode", sms_code);
//        params.put("verifCode", imgCodeId);
        HttpManager.getInstance().post(AppNetConfig.SMS_INVITATION, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                dismissProgress();
                if (isFinishing() || obj == null) {
                    return;
                }
                if (obj.isSuccess()) {
                    if (!TextUtils.isEmpty(obj.msg)) {
                        UiUtils.toast(obj.msg);
                    }
                    finish();
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

        });

    }

}
