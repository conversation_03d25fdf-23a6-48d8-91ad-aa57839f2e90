package com.ybmmarket20.activity;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Rect;
import android.os.Bundle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.Rows2Bean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.ButterKnife;

/**
 * 保驾护航 选择申请商品
 */
@Router({"selectapplyforgoods", "selectapplyforgoods/:orderNo", "selectapplyforgoods/:orderNo/:mobile/:contactor"})
public class selectApplyForGoodsActivity extends BaseActivity {

    @Bind(R.id.crv_list)
    CommonRecyclerView crvList;

    private YBMBaseAdapter adapter;
    private List<Rows2Bean> rows = new ArrayList<>();
    private int bottom = com.ybm.app.utils.UiUtils.dp2px(6);
    private String orderNo;
    private String mobile;
    private String contactor;
    private int pageSize = 10;
    private int pager = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initReceiver();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_select_apply_for_goods;
    }

    @Override
    protected void initData() {

        setTitle("选择申请商品");
        orderNo = getIntent().getStringExtra("orderNo");
        mobile = getIntent().getStringExtra("mobile");
        contactor = getIntent().getStringExtra("contactor");

        adapter = new YBMBaseAdapter<Rows2Bean>(R.layout.item_apply_for_goods, rows) {

            @Override
            protected void bindItemView(YBMBaseHolder baseViewHolder, Rows2Bean bean) {

                baseViewHolder.setImageUrl(R.id.iv_order, AppNetConfig.LORD_IMAGE + bean.imageUrl, R.drawable.jiazaitu_min)
                        .setText(R.id.tv_order_name, bean.productName)
                        .setText(R.id.tv_spec, "规格:" + bean.spec)
                        .setText(R.id.tv_price, "单价:¥" + UiUtils.transform(bean.productPrice))
                        .setText(R.id.tv_num, "数量:" + bean.productAmount)
                        .setText(R.id.tv_subtotal, "小计:¥" + bean.subTotal)
                        .getConvertView().setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        ApplyForConvoyActivity.getInstance(selectApplyForGoodsActivity.this, mobile, contactor, bean);
//                        RoutersUtils.open("ybmpage://applyforconvoy/" + orderNo);
                    }
                });
            }
        };
        crvList.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {

                getData(pager = 0);

            }

            @Override
            public void onLoadMore() {
                getData(pager);
            }
        });
        crvList.setEnabled(false);
        crvList.setAdapter(adapter);
        crvList.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "暂无可申请订单");
//        adapter.openLoadMore(10, true);
        adapter.setEnableLoadMore(false);
        crvList.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                outRect.bottom = bottom;
            }
        });

    }

    public void getData(final int page) {

        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
//        params.put("limit", String.valueOf(pageSize));
//        params.put("offset", String.valueOf(String.valueOf(page)));
        if (!TextUtils.isEmpty(orderNo)) {
            params.put("orderNo", orderNo);
        }

        HttpManager.getInstance().post(AppNetConfig.FIND_ORDERS_ESCORT_DETAIL, params, new BaseResponse<List<Rows2Bean>>() {

            @Override
            public void onSuccess(String content, BaseBean<List<Rows2Bean>> brandBean, List<Rows2Bean> rowsBeans) {
                completion();
                if (brandBean != null) {
                    if (brandBean.isSuccess()) {

                        if (rowsBeans != null) {

                            if (rowsBeans != null && rowsBeans.size() > 0) {
                                if (page <= 0) {
                                    selectApplyForGoodsActivity.this.pager = 1;
                                } else {
                                    selectApplyForGoodsActivity.this.pager++;
                                }
                            }
                            if (page <= 0) {

                                if (rows == null) {
                                    rows = new ArrayList<>();
                                }

                                if (rows.size() <= 0 && rowsBeans != null) {
                                    rows.addAll(rowsBeans);
                                } else {
                                    if (rowsBeans == null || rowsBeans.isEmpty()) {

                                    } else {

                                        rows.addAll(0, rowsBeans);
                                    }
                                }
                                adapter.setNewData(rows);
                                adapter.notifyDataChangedAfterLoadMore(rows.size() >= pageSize);
                            } else {
                                if (rowsBeans == null || rowsBeans.size() <= 0) {
                                    adapter.notifyDataChangedAfterLoadMore(false);
                                } else {
                                    rows.addAll(rowsBeans);
                                    adapter.setNewData(rows);
                                    adapter.notifyDataChangedAfterLoadMore(rowsBeans.size() >= pageSize);
                                }
                            }
                        }
                    } else {
                        adapter.setNewData(rows);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                if (crvList != null) {
                    crvList.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                if (crvList != null) {
                                    adapter.setNewData(rows);
                                }
                            } catch (Throwable e) {
                                BugUtil.sendBug(e);
                            }
                        }
                    }, 300);
                }
            }
        });

    }

    private void completion() {
        if (crvList != null) {
            try {
                crvList.setRefreshing(false);
            } catch (Throwable e) {
                BugUtil.sendBug(e);
            }
        }
    }


    private void initReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.FINISH_THE_ESCORT);
        LocalBroadcastManager.getInstance(this.getApplicationContext()).registerReceiver(mRefreshBroadcastReceiver, intentFilter);
    }

    /*
     * 广播
     * */
    private BroadcastReceiver mRefreshBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (IntentCanst.FINISH_THE_ESCORT.equals(action)) {
                finish();
            }
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ButterKnife.unbind(this);
        if (mRefreshBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(this.getApplicationContext()).unregisterReceiver(mRefreshBroadcastReceiver);
        }
    }

}
