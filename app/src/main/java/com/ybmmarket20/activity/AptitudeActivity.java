package com.ybmmarket20.activity;

import static com.ybmmarket20.constant.IntentCanst.RX_BUS_LICENSE_STATUS;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_NET_ERR;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_UPDATE_LICENCEDTAIL;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.analysys.ANSAutoPageTracker;
import com.flyco.tablayout.SlidingTabLayout;
import com.github.mzule.activityrouter.annotation.Router;
import com.luck.picture.lib.tools.DoubleUtils;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AptitudeDetailBean;
import com.ybmmarket20.bean.AptitudeEvent;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.fragments.AptitudeAuditFragment;
import com.ybmmarket20.fragments.AptitudeRequiredFragment;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarketkotlin.fragments.AptitudeOverdueDialogFragment;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import butterknife.Bind;
import butterknife.OnClick;


/**
 * 资质管理
 */
@Router("aptitude")
public class AptitudeActivity extends BaseActivity implements ANSAutoPageTracker {
    @Bind(R.id.tabLayout)
    SlidingTabLayout tabLayout;
    @Bind(R.id.viewPager)
    ViewPager viewPager;
    @Bind(R.id.tv_download)
    TextView tvDownload;
    @Bind(R.id.tv_operation)
    TextView tvOperation;
    @Bind(R.id.ll_bottom)
    LinearLayout ll_bottom;
    public static int TYPE_APTITUDE_REQUIRED = 100;
    public static int TYPE_APTITUDE_AUDIT = 101;
    public static int APTITUDE_FIRSTTYPE = 1;//首营资质
    public static String EXTRA_APTITUDE_TABINDEX = "extra_aptitude_tabIndex";
    private String[] titles = new String[]{"资质列表", "变更记录"};
    private ArrayList<Fragment> fragmentArrayList;
    private Fragment aptitudeRequiredFragment;
    private Fragment aptitudeAuditFragment;

    //资质状态
    private String merchantId = "";
    private int licenseStatus = APTITUDE_FIRSTTYPE;
    private String firstType = "1";

    private AptitudeDetailBean aptitudeDetailBean;  // 资质相关信息
    private boolean needCheckAptitudeStatus = true;

    @Override
    protected void initData() {
        setTitle("资质管理");

        getAptitudeInfo();
        initTab();
        merchantId = SpUtil.getMerchantid();
        if (licenseStatus == APTITUDE_FIRSTTYPE) {
            //首营资质
            tvOperation.setText("添加首营资质");
        } else {
            tvOperation.setText("资质变更");
        }
        findViewById(R.id.iv_back).setOnClickListener(v -> onBackPressed());
    }


    private void initTab() {
        fragmentArrayList = new ArrayList<>();

        aptitudeRequiredFragment = new AptitudeRequiredFragment();
        Bundle requiredBundle = new Bundle();
        requiredBundle.putInt(EXTRA_APTITUDE_TABINDEX, TYPE_APTITUDE_REQUIRED);
        aptitudeRequiredFragment.setArguments(requiredBundle);

        aptitudeAuditFragment = new AptitudeAuditFragment();
        Bundle auditBundle = new Bundle();
        auditBundle.putInt(EXTRA_APTITUDE_TABINDEX, TYPE_APTITUDE_AUDIT);
        aptitudeAuditFragment.setArguments(auditBundle);
        //资质列表
        fragmentArrayList.add(aptitudeRequiredFragment);
        //变更记录
        fragmentArrayList.add(aptitudeAuditFragment);

        tabLayout.setViewPager(viewPager, titles, this, fragmentArrayList);

        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                setPositionListener(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

    }

    public void setPositionListener(int position) {
        switch (position) {
            case 0:
                JGTrackTopLevelKt.jgTrackAptitudeBtnClick(this, "资质列表");
                break;
            case 1:
                JGTrackTopLevelKt.jgTrackAptitudeBtnClick(this, "变更记录");
                break;
        }
    }


    /**
     * 获取资质列表的信息
     */
    public void getAptitudeInfo() {
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        HttpManager.getInstance().post(AppNetConfig.LICENSE_AUDIT_FIND_LICENSE_LIST, params, new BaseResponse<AptitudeDetailBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<AptitudeDetailBean> baseBean, AptitudeDetailBean data) {
                dismissProgress();
                if (baseBean != null && baseBean.isSuccess()) {
                    aptitudeDetailBean = data;
                }

            }
        });
    }


    private void processOnBackPressed(AptitudeDetailBean data) {
        if (data == null || data.licenseList == null || data.licenseList.size() == 0) {
            finish();
            return;
        }
        AptitudeOverdueDialogFragment aodf = AptitudeOverdueDialogFragment.Companion.getInstance(2, data.licenseList);
        aodf.show(getSupportFragmentManager(), "xxx");
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_new_aptitude;
    }

    @Override
    protected boolean isRegisterEventBus() {
        return true;
    }

    @Override
    protected void receiveEvent(Event event) {
        if (event.getCode() == RX_BUS_UPDATE_LICENCEDTAIL) {
            boolean isSubscribe = (boolean) event.getData();
            if (isSubscribe) {//刷新数据

            }
        } else if (event.getCode() == RX_BUS_LICENSE_STATUS) {
            AptitudeEvent aptitudeEvent = (AptitudeEvent) event.getData();
            licenseStatus = aptitudeEvent.getLicenseStatus();
            firstType = aptitudeEvent.getFirstLicenseType();
            if (licenseStatus == APTITUDE_FIRSTTYPE) {
                //首营资质
                tvOperation.setText("添加首营资质");
            } else {
                tvOperation.setText("资质变更");
            }
        } else if (event.getCode() == RX_BUS_NET_ERR) {
            boolean netIsErr = (boolean) event.getData();
            ll_bottom.setVisibility(netIsErr ? View.GONE : View.VISIBLE);
        } else if (event.getCode() == RX_BUS_UPDATE_LICENCEDTAIL) {
            // 更新资质成功
            needCheckAptitudeStatus = false;
        }
    }


    @OnClick({R.id.tv_download, R.id.tv_operation})
    public void clickTab(View view) {
        if (!DoubleUtils.isFastDoubleClick()) {
            switch (view.getId()) {
                case R.id.tv_operation://添加或变更首营资质
                    ((YBMAppLike)getApplication()).mineSelectShop = true;
                    RoutersUtils.open("ybmpage://aptitudebasicinfo?licenseStatus=" + licenseStatus);
                    JGTrackTopLevelKt.jgTrackAptitudeBtnClick(this, "资质变更");
                    break;
            }
        }
    }

    @Override
    public void onBackPressed() {
        if (needCheckAptitudeStatus && (SpUtil.getValidityStatus() == 1 || SpUtil.getValidityStatus() == 2)) {
            processOnBackPressed(aptitudeDetailBean);
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ((YBMAppLike)getApplication()).mineSelectShop = false;
    }

    @Override
    public Map<String, Object> registerPageProperties() {
        HashMap<String,Object> properties = new HashMap<>();
        properties.put(JGTrackManager.FIELD.FIELD_PAGE_ID, JGTrackManager.TrackAptitude.PAGE_ID);
        properties.put(JGTrackManager.FIELD.FIELD_TITLE, JGTrackManager.TrackAptitude.TITLE);
        return properties;
    }

    @Override
    public String registerPageUrl() {
        return AppUtilKt.getFullClassName(AptitudeActivity.this);
    }
}