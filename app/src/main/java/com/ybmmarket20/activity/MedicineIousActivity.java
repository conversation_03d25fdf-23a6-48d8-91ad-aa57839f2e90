package com.ybmmarket20.activity;

import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.FinancesBean;
import com.ybmmarket20.bean.MedicineIousBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.widget.RoundLinearLayout;
import com.ybmmarket20.common.widget.RoundRelativeLayout;
import com.ybmmarket20.common.widget.RoundTextView;
import com.ybmmarket20.common.widget.RoundedImageView;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.MathUtils;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.SpanUtils;
import com.ybmmarket20.utils.UiUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 小药白条
 */
@Router({"whiteBarDetail", "whiteBarDetail/:finance_code"})
public class MedicineIousActivity extends BaseActivity {

    @Bind(R.id.tv_hint)
    TextView tvHint;
    @Bind(R.id.iv_bg)
    RoundedImageView ivBg;
    @Bind(R.id.tv_lines_info)
    RoundTextView tvLinesInfo;
    @Bind(R.id.tv_account_money)
    RoundTextView tvAccountMoney;
    @Bind(R.id.tv_a_combination_info)
    TextView tvACombinationInfo;
    @Bind(R.id.tv_a_combination_money)
    TextView tvACombinationMoney;
    @Bind(R.id.ll_a_combination)
    RoundLinearLayout llACombination;
    @Bind(R.id.tv_amount_to_be_also_info)
    TextView tvAmountToBeAlsoInfo;
    @Bind(R.id.tv_amount_to_be_also_money)
    TextView tvAmountToBeAlsoMoney;
    @Bind(R.id.ll_amount_to_be_also)
    RoundLinearLayout llAmountToBeAlso;
    @Bind(R.id.ll_bg_info)
    RoundLinearLayout llBgInfo;
    @Bind(R.id.rl_transaction_details)
    RoundRelativeLayout rlTransactionDetails;
    @Bind(R.id.tv_interest_subsidy)
    TextView tvInterestSubsidy;
    @Bind(R.id.iv_interest_subsidy)
    RoundedImageView ivInterestSubsidy;
    @Bind(R.id.tv_transaction_details)
    TextView tvTransactionDetails;
    @Bind(R.id.iv_transaction_details)
    RoundedImageView ivTransactionDetails;
    @Bind(R.id.rl_interest_subsidy)
    RoundRelativeLayout rlInterestSubsidy;
    @Bind(R.id.rl_common_problems)
    RoundRelativeLayout rlCommonProblems;
    @Bind(R.id.apply_status_review_the_rejected)
    RoundRelativeLayout applyStatusReviewTheRejected;
    @Bind(R.id.apply_status_through)
    RoundLinearLayout applyStatusThrough;
    @Bind(R.id.iv_review_rejected)
    RoundedImageView ivReviewRejected;
    @Bind(R.id.tv_review_rejected_info)
    TextView tvReviewRejectedInfo;
    @Bind(R.id.tv_review_rejected_time)
    TextView tvReviewRejectedTime;
    @Bind(R.id.tv_review_rejected_prompt)
    TextView tvReviewRejectedPrompt;
    @Bind(R.id.tv_review_rejected_warm_prompt)
    TextView tvReviewRejectedWarmPrompt;

    private String financeCode;
    private SimpleDateFormat mFormat;

    public static final int APPLY_STATUS_REVIEW = 2;//审核中

    public static final int APPLY_STATUS_THROUGH = 3;//通过

    public static final int APPLY_STATUS_REJECTED = 4;//驳回

    @Override
    protected int getContentViewId() {
        return R.layout.activity_medicine_ious;
    }

    @Override
    protected void initData() {
        setTitle("小药白条");

        financeCode = getIntent().getStringExtra("finance_code");
        mFormat = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss", Locale.getDefault());

        SpannableStringBuilder stringBuilder = new SpanUtils()
                .appendImage(R.drawable.icon_notice, SpanUtils.ALIGN_BASELINE)
                .appendSpace(UiUtils.dp2px(2))
                .append(getResources().getString(R.string.medicine_ious_hint))
                .create();
        tvHint.setText(stringBuilder);

        getData();
    }

    /*
     * 请求服务端数据
     * */
    private void getData() {
        HttpManager.getInstance().post(AppNetConfig.FINANCE_GFBT_GETIOUSINFO, getRefreshParams(), new BaseResponse<MedicineIousBean>() {
            @Override
            public void onSuccess(String content, BaseBean<MedicineIousBean> obj, MedicineIousBean data) {
                if (obj != null && obj.isSuccess() && data != null) {
                    setData(data);
                }
            }
        });
    }

    /**
     * 参数信息
     */
    private RequestParams getRefreshParams() {
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("financeCode", financeCode);//金融产品编码
        return params;
    }

    /*
     * 设置数据
     * */
    private void setData(MedicineIousBean data) {
        if (applyStatusReviewTheRejected == null) {
            return;
        }
        if (data != null && data.getMerchantData() != null) {
            FinancesBean financesBean = data.getMerchantData();
            int applyStatus = financesBean.getApplyStatus();
            if (applyStatus == APPLY_STATUS_REVIEW || applyStatus == APPLY_STATUS_REJECTED) {
                applyStatusReviewTheRejected.setVisibility(View.VISIBLE);
                applyStatusThrough.setVisibility(View.GONE);
                initReviewAndRejectedStatus(financesBean);
            } else if (applyStatus == APPLY_STATUS_THROUGH) {
                applyStatusReviewTheRejected.setVisibility(View.GONE);
                applyStatusThrough.setVisibility(View.VISIBLE);
                initThroughStatus(financesBean);
                initEmptyData(data);
            }
        }
    }

    /*
     * 审核中和驳回
     * */
    private void initReviewAndRejectedStatus(FinancesBean finances) {
        if (ivReviewRejected == null) {
            return;
        }
        if (finances.getApplyStatus() == APPLY_STATUS_REVIEW) {
            ivReviewRejected.setImageResource(R.drawable.icon_medicine_ious_review);
            tvReviewRejectedInfo.setText("额度审核中");
            tvReviewRejectedTime.setText(mFormat.format(new Date(finances.getCreateTime())));
            tvReviewRejectedPrompt.setText(getResources().getString(R.string.medicine_ious_review_prompt_hint));
            tvReviewRejectedWarmPrompt.setVisibility(View.GONE);
        } else if (finances.getApplyStatus() == APPLY_STATUS_REJECTED) {
            ivReviewRejected.setImageResource(R.drawable.icon_medicine_ious_rejected);
            tvReviewRejectedInfo.setText("额度审核失败");
            tvReviewRejectedTime.setText(mFormat.format(new Date(finances.getCreateTime())));
            tvReviewRejectedPrompt.setText(getResources().getString(R.string.medicine_ious_rejected_prompt_hint));
            tvReviewRejectedWarmPrompt.setVisibility(View.VISIBLE);
        }

    }

    /*
     * 审核通过
     * */
    private void initThroughStatus(FinancesBean finances) {
        if (tvAccountMoney == null) {
            return;
        }
        //可用额度
        tvAccountMoney.setText(MathUtils.getFormatNum(finances.getCreditAvailable()));
        //总额度
        tvACombinationMoney.setText(MathUtils.getFormatNum(finances.getCreditLine()));
        //待还额度
        tvAmountToBeAlsoMoney.setText(MathUtils.getFormatNum(finances.getCreditUsed()));

    }

    /*
    * 有无明细
    * */
    private void initEmptyData(MedicineIousBean data) {
        if (tvTransactionDetails == null) {
            return;
        }

        tvTransactionDetails.setVisibility(!TextUtils.isEmpty(data.getTransMsg()) ? View.VISIBLE : View.GONE);
        ivTransactionDetails.setVisibility(!TextUtils.isEmpty(data.getTransMsg()) ? View.GONE : View.VISIBLE);
        tvTransactionDetails.setText(!TextUtils.isEmpty(data.getTransMsg()) ? data.getTransMsg() : getResources().getString(R.string.medicine_ious_empty_hint));

        tvInterestSubsidy.setVisibility(!TextUtils.isEmpty(data.getInterestMsg()) ? View.VISIBLE : View.GONE);
        ivInterestSubsidy.setVisibility(!TextUtils.isEmpty(data.getInterestMsg()) ? View.GONE : View.VISIBLE);
        tvInterestSubsidy.setText(!TextUtils.isEmpty(data.getInterestMsg()) ? data.getInterestMsg() : getResources().getString(R.string.medicine_ious_empty_hint));

        rlTransactionDetails.setEnabled(TextUtils.isEmpty(data.getTransMsg()));
        rlInterestSubsidy.setEnabled(TextUtils.isEmpty(data.getInterestMsg()));

    }

    @OnClick({R.id.rl_transaction_details, R.id.rl_interest_subsidy, R.id.rl_common_problems})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.rl_transaction_details:
                RoutersUtils.open("ybmpage://transactiondetails?financeCode=" + financeCode);
                break;
            case R.id.rl_interest_subsidy:
                RoutersUtils.open("ybmpage://interestsubsidy?financeCode=" + financeCode);
                break;
            case R.id.rl_common_problems:
                RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.MY_BANKING_XYBT_FAQ);
                break;
        }
    }

}
