package com.ybmmarket20.activity.afterSales.adapter

import com.ybm.app.adapter.YBMBaseAdapter

abstract class YBMNotifyAdapter<T>(layoutId: Int, data: List<T>) :
    YBMBaseAdapter<T>(layoutId, data), IYBMNotifyAdapter {

    private var mCallback: ((Boolean) -> Unit)? = null

    //防止在状态不变的情况下重复通知
    private var mCurrentSelectedStatus = false


    override fun enableNotify(): Boolean = false

    override fun addNotify(callback: ((Boolean) -> Unit)?) {
        mCallback = callback
    }

    override fun notifyEdit(isSelected: Boolean) {
        if (isSelected == mCurrentSelectedStatus) return
        mCurrentSelectedStatus = isSelected
        mCallback?.invoke(isSelected)
    }

}