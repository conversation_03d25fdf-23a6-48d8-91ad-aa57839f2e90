package com.ybmmarket20.activity.jdpay.adapter

import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.BankCardItem
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.homesteady.whenAllNotNull

class BankCardAdapter(list: MutableList<BankCardItem>): YBMBaseAdapter<BankCardItem>(R.layout.item_bank_card, list) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: BankCardItem?) {
        whenAllNotNull(baseViewHolder, t) {holder, bean ->
            ImageUtil.load(mContext, bean.bankLogo, holder.getView(R.id.ivLogo))
            holder.setText(R.id.tvBankName, bean.bankShortName)
            holder.setText(R.id.tvBankCardType, bean.cardTypeStr)
            holder.setText(R.id.tvBankCardNum, bean.cardNo)
            holder.itemView.setOnClickListener {
                val bankCardJson = RoutersUtils.getBase64BeanToString(bean)
                RoutersUtils.open("ybmpage://mybankcardlist?bankInfo=$bankCardJson")
            }
        }
    }
}