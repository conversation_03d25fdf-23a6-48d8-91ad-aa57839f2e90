package com.ybmmarket20.search

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.common.util.ConvertUtils

class SearchRecommendItemDecoration : RecyclerView.ItemDecoration() {
    private val space = ConvertUtils.dp2px(1f)
    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {

        if (parent.getChildLayoutPosition(view) % 2 == 1) {
            outRect.left = space * 6
            outRect.right = space * 12
        } else {
            outRect.left = space * 12
            outRect.right = space * 6
        }
    }
}