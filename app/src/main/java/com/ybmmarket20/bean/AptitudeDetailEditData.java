package com.ybmmarket20.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;

public class AptitudeDetailEditData implements Parcelable {

    public String aptitudeId;
    public String ecOrgCode;
    public boolean isAdd;
    public boolean isDraft;
    public String remark;
    public String tempRemark;
    public String code;
    public String status;
    public int statusCode;
    public String licenseAuditId;
    public String time;
    public String from;
    public ArrayList<LicenceBean> mNecessary;
    public ArrayList<LicenceBean> mOptional;


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.aptitudeId);
        dest.writeString(this.ecOrgCode);
        dest.writeByte(this.isAdd ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isDraft ? (byte) 1 : (byte) 0);
        dest.writeString(this.remark);
        dest.writeString(this.tempRemark);
        dest.writeString(this.code);
        dest.writeString(this.status);
        dest.writeInt(this.statusCode);
        dest.writeString(this.licenseAuditId);
        dest.writeString(this.time);
        dest.writeString(this.from);
        dest.writeTypedList(this.mNecessary);
        dest.writeTypedList(this.mOptional);
    }

    public void readFromParcel(Parcel source) {
        this.aptitudeId = source.readString();
        this.ecOrgCode = source.readString();
        this.isAdd = source.readByte() != 0;
        this.isDraft = source.readByte() != 0;
        this.remark = source.readString();
        this.tempRemark = source.readString();
        this.code = source.readString();
        this.status = source.readString();
        this.statusCode = source.readInt();
        this.licenseAuditId = source.readString();
        this.time = source.readString();
        this.from = source.readString();
        this.mNecessary = source.createTypedArrayList(LicenceBean.CREATOR);
        this.mOptional = source.createTypedArrayList(LicenceBean.CREATOR);
    }

    public AptitudeDetailEditData() {
    }

    public AptitudeDetailEditData(String aptitudeId, String ecOrgCode, boolean isAdd, boolean isDraft, String remark, String tempRemark, String code, String status, int statusCode, String licenseAuditId, String time, String from, ArrayList<LicenceBean> mNecessary, ArrayList<LicenceBean> mOptional) {
        this.aptitudeId = aptitudeId;
        this.ecOrgCode = ecOrgCode;
        this.isAdd = isAdd;
        this.isDraft = isDraft;
        this.remark = remark;
        this.tempRemark = tempRemark;
        this.code = code;
        this.status = status;
        this.statusCode = statusCode;
        this.licenseAuditId = licenseAuditId;
        this.time = time;
        this.from = from;
        this.mNecessary = mNecessary;
        this.mOptional = mOptional;
    }

    protected AptitudeDetailEditData(Parcel in) {
        this.aptitudeId = in.readString();
        this.ecOrgCode = in.readString();
        this.isAdd = in.readByte() != 0;
        this.isDraft = in.readByte() != 0;
        this.remark = in.readString();
        this.tempRemark = in.readString();
        this.code = in.readString();
        this.status = in.readString();
        this.statusCode = in.readInt();
        this.licenseAuditId = in.readString();
        this.time = in.readString();
        this.from = in.readString();
        this.mNecessary = in.createTypedArrayList(LicenceBean.CREATOR);
        this.mOptional = in.createTypedArrayList(LicenceBean.CREATOR);
    }

    public static final Creator<AptitudeDetailEditData> CREATOR = new Creator<AptitudeDetailEditData>() {
        @Override
        public AptitudeDetailEditData createFromParcel(Parcel source) {
            return new AptitudeDetailEditData(source);
        }

        @Override
        public AptitudeDetailEditData[] newArray(int size) {
            return new AptitudeDetailEditData[size];
        }
    };
}
