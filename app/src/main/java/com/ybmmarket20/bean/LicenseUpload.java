package com.ybmmarket20.bean;

import android.text.TextUtils;

import com.ybmmarket20.adapter.LicensePicListAdapter;

import java.util.List;

public class LicenseUpload {

    public String id;
    /**
     * 资质分类编码
     */
//  public String licenseCategoryCode;//废弃
    public String licenseImgUrls;//资质图片集合
    public String licenseCode;//资质类型编码

    public long xyyEntrusValidateTime;
    public String xyyEntrusCode;
    /**
     * 资质名称
     */
    public String name;
    public int isRequired;//1.是必须，2.是非必须

    public LicenseUpload(LicenceBean licenceBean) {
        this.id = licenceBean.id;
//        this.licenseCategoryCode = licenceBean.categoryCode;
        licenseImgUrls = parseList2Str(licenceBean.adapter.getData());
        this.licenseCode = licenceBean.categoryCode;
        this.xyyEntrusValidateTime = licenceBean.xyyEntrusValidateTime;
        this.xyyEntrusCode = licenceBean.xyyEntrusCode;
        this.isRequired = licenceBean.isRequired;
    }

    public String parseList2Str(List<LicensePicListAdapter.ImageInfo> infoList) {
        if (infoList.size() == 1) {
            //认为只有一个+号
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (LicensePicListAdapter.ImageInfo imageInfo : infoList) {
            if (!TextUtils.isEmpty(imageInfo.newPath)) {
                //只要有一个更新的地址就认为该条目需要更新
                stringBuilder.append(imageInfo.newPath);
                stringBuilder.append(",");

            } else if (!LicensePicListAdapter.EDIT_FLAG.equals(imageInfo.oldPath) && !TextUtils.isEmpty(imageInfo.oldPath)) {
                //去除+号的空数据
                stringBuilder.append(imageInfo.oldPath);
                stringBuilder.append(",");

            }
        }
        if (stringBuilder.length() == 0) {
            return null;
        } else {
            StringBuilder stringBuilder1 = stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            return stringBuilder1.toString();
        }


    }


}