package com.ybmmarket20.bean

import com.chad.library.adapter.base.entity.MultiItemEntity
import com.google.gson.annotations.SerializedName
import com.ybmmarket20.bean.loadmore.IPage
import com.ybmmarket20.xyyreport.spm.TrackData

class CouponBean<T>(
        val currentPage: Int,
        val expiredNum: Int,
        val limit: Int,
        val offset: Int,
        val rows: List<T>?,
        val total: Int,
        val unsueNum: Int,
        val usedNum: Int,
        private val totalPage: Int
): IPage<T> {
    override fun getCurPage(): Int = offset

    override fun getPageRowSize(): Int = limit

    override fun getTotalPages(): Int = totalPage

    override fun getRowsList(): List<T>? = rows

}


open class CouponRowBean(
        open val appUrl: String?,
        val discount: Double,                   // 如果该值大于零，则是打折券
        var discountStr: String?,               // 判断 打折类型展示的逻辑：若discount有值且大于0，则显示discountStr，字符串类型，表示 几折。
        val expireDate: Long,
        val fanliDesc: String?,
        val id: Int,
        val isUse: Int,
        val minMoneyToEnable: String?,
        val minMoneyToEnableDesc: String?,
        val moneyInVoucher: Double,
        val pcUrl: String?,
        var state: Int,                         // 券状态（1-未领取，2-已领取，3-已使用，4-失效，5-已刪除）
        val skuRelationType: Int,               // 1:全部商品参与 2:指定商品参与 3:指定商品不参与
        val validDate: Long,
        val voucherId: String,
        val voucherInstructions: String?,
        val voucherState: Int,                  // 不用该状态做判断
        val voucherTemplateId: String?,
        val templateId: String?,                // 意义同voucherTemplateId
        val voucherTitle: String?,              // 券面文案
        val voucherType: Int,
        val voucherTypeDesc: String?,           // 券模板类型(1-通用券，2-厂家券，3-折扣券，4-礼品券,5-新人券，6-叠加券 ,8-平台券, 9-专品券)说明
        val voucherUsageWay: String?,           // 券模板类型(1-通用券，2-厂家券，3-折扣券，4-礼品券,5-新人券，6-叠加券,8-平台券, 9-专品券)
        val maxMoneyInVoucherDesc: String?,
        val voucherSkuImages: MutableList<CouponScopeGoods>?,
        var isUnFold: Boolean = false,      // item 中的可用券商品是否折叠
        var shopName:String?,
        var validDayStr: String?,
        var couponDutyDepartLabel: String?,
        var invalidFlag: Int, //state==4时使用，1：已过期.2：已失效
        val invalidReason: String?,//state=4时使用，过期或失效原因
        var rightsType: Int,
        var redPacketOriginMoney: String?, //红包可用金额
        var templateName: String?, //名称（红包
        var validDateToString: String?, //红包生效时间
        var expireDateToString: String?, //失效时间
        var marketingId: String?, //活动id
        val jgspid: String?, //来源ID
        val componentPosition: String?,
        val componentName: String?,
        val componentTitle: String?,
        val trackData: TrackData?
): MultiItemEntity {
    override fun getItemType(): Int {
        if(rightsType != 1 && rightsType != 2) {
            //防止后端乱返回数据，导致addItemType未定义类型找不到布局crash
            return 1
        }
        return rightsType
    }

}