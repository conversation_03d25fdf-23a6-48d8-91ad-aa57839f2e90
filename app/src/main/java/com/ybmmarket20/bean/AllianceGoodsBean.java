package com.ybmmarket20.bean;

import java.util.List;

/**
 * Author ： LoveNewsweetheart
 * Date:2019/4/11
 */
public class AllianceGoodsBean {


    private boolean isExtendsStatus = false;//是否扩展状态

    private String advisePatients;// 建议患者---温馨提示
    private String branchCode;
    private String commonSense;//常识判断-----病因
    private String drugId;
    private String generalEffect;//一般用药/作用----推荐用药
    private String keyWord;
    private String medicationPrinciple;//用药原则-----治疗原则
    private String patientSymptoms;//患者症状
    private String approvalNumber;
    private String title;

    private List<RowsBean> products;

    public List<RowsBean> getProducts() {
        return products;
    }

    public void setProducts(List<RowsBean> products) {
        this.products = products;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAdvisePatients() {
        return advisePatients;
    }

    public void setAdvisePatients(String advisePatients) {
        this.advisePatients = advisePatients;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getCommonSense() {
        return commonSense;
    }

    public void setCommonSense(String commonSense) {
        this.commonSense = commonSense;
    }

    public String getDrugId() {
        return drugId;
    }

    public void setDrugId(String drugId) {
        this.drugId = drugId;
    }

    public String getGeneralEffect() {
        return generalEffect;
    }

    public void setGeneralEffect(String generalEffect) {
        this.generalEffect = generalEffect;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getMedicationPrinciple() {
        return medicationPrinciple;
    }

    public void setMedicationPrinciple(String medicationPrinciple) {
        this.medicationPrinciple = medicationPrinciple;
    }

    public String getPatientSymptoms() {
        return patientSymptoms;
    }

    public void setPatientSymptoms(String patientSymptoms) {
        this.patientSymptoms = patientSymptoms;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public boolean isExtendsStatus() {
        return isExtendsStatus;
    }

    public void setExtendsStatus(boolean extendsStatus) {
        isExtendsStatus = extendsStatus;
    }
}
