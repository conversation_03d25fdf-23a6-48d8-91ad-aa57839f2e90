package com.ybmmarket20.bean;

import java.util.List;

/**
 * Created by asus on 2016/3/31.
 */
public class SortBean {

    private List<OneRowsBean> categoryList;
    private int isFragile;//取数量的标识,0：取skuContainFragileNum； 1：取skuNotContainFragileNum

    public List<OneRowsBean> getCategoryList() {
        return categoryList;
    }

    public int getIsFragile() {
        return isFragile;
    }

}
