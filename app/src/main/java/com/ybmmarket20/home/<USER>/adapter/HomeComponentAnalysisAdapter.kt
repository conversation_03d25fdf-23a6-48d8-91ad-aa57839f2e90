package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.xyyreport.page.home.HomeReportEvent
import com.ybmmarket20.xyyreport.spm.TrackData

abstract class HomeComponentAnalysisAdapter<T: RecyclerView.ViewHolder>: RecyclerView.Adapter<T>(), IHomeComponentAnalysis {

    protected val mExposureRecord = mutableSetOf<Int>()

    private var mCacheTrackData: TrackData? = null
    private var mCacheBlock: (()-> Unit)? = null
    private var mCacheContext: Context? = null
    private var mCachePosition: Int = 0

    override fun onComponentExposure(context: Context, trackData: TrackData?, position: Int, block: (()-> Unit)?) {
        if (mExposureRecord.contains(position)) return
        mCacheTrackData = trackData
        mCacheBlock = block
        mCacheContext = context
        mCachePosition = position
        block?.invoke()
        HomeReportEvent.trackHomeComponentExposure(context, trackData?.spmEntity)
        mExposureRecord.add(position)
    }

    override fun onSubcomponentClick(context: Context, trackData: TrackData?) {
        HomeReportEvent.trackHomeSubComponentClick(context, trackData?.spmEntity, trackData?.scmEntity)
    }

    override fun resetExposureRecord(){
        mExposureRecord.clear()
    }

    override fun reComponentExposure() {
        mCacheBlock?.invoke()
        mCacheContext?.let { HomeReportEvent.trackHomeComponentExposure(it, mCacheTrackData?.spmEntity) }
    }
}