package com.ybmmarket20.home

import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.ybmmarket20.R
import com.ybmmarket20.common.util.Abase.getResources
import com.ybmmarket20.utils.RoutersUtils

interface CartFragmentCommonImpl {


    /**
     * 设置购物车title栏左侧
     */
    fun setBackTitle(titleName: TextView?, action: String? = null) {
        var url = RoutersUtils.decodeRAWUrl(action)
        titleName?.let {

            if (TextUtils.isEmpty(url)) {
                titleName.setCompoundDrawables(null, null, null, null)
            } else {
                val leftDrawable: Drawable = getResources().getDrawable(R.drawable.ic_back)
                leftDrawable.setBounds(0, 0, leftDrawable.minimumWidth, leftDrawable.minimumHeight)
                titleName.setCompoundDrawables(leftDrawable, null, null, null)
                titleName.setOnClickListener {
                    if (RoutersUtils.open(url)){
                        url = ""
                        titleName.setCompoundDrawables(null, null, null, null)
                    }
                }
            }
        }
    }
}