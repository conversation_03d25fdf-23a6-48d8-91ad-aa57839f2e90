package com.ybmmarket20.home.mine

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.ybmmarket20.R
import com.ybmmarket20.home.mine.bean.Mine2HeaderBean
import com.ybmmarket20.home.mine.bean.Mine2HeaderItemContainer
import com.ybmmarket20.message.Message
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import kotlinx.android.synthetic.main.view_mine2_header.view.*

/**
 * 我的头部View
 */
class Mine2HeaderView(context: Context, attr: AttributeSet?) : AbsMine2BaseView<Mine2HeaderBean>(context, attr), View.OnClickListener {

    override fun initialize() {
        super.initialize()
        tv_mine2_more_account.setOnClickListener(this)
        iv_mine2_setting.setOnClickListener(this)
        iv_mine2_message.setOnClickListener(this)
    }

    override fun getLayoutId(): Int = R.layout.view_mine2_header

    override fun setData(container: Mine2HeaderItemContainer<Mine2HeaderBean>) {
        container.entry?.let {
            tv_mine2_merchant_name.text = it.merchantName
        }
    }

    /**
     * 设置消息数量
     */
    fun setMessageCount(messageCount: Int) {
        Message.showMsgCount2(messageCount, tv_mine2_message_bubble)
    }

    override fun onClick(v: View?) {
        when(v?.id) {
            //更多账号信息
            R.id.tv_mine2_more_account -> {
                RoutersUtils.open("ybmpage://accountbasicinfo")
                XyyIoUtil.track("action_Me_MoreAccount")
                MineFragment2.jgTrackBtnClick(context,"顶部功能","更多账号信息")
            }
            //设置
            R.id.iv_mine2_setting -> {
                RoutersUtils.open("ybmpage://elsepage")
                XyyIoUtil.track("action_Me_Install")
                MineFragment2.jgTrackBtnClick(context,"顶部功能","设置")
            }
            //消息
            R.id.iv_mine2_message -> {
                Message.openMessagePage()
                XyyIoUtil.track("action_Me_Message")
                MineFragment2.jgTrackBtnClick(context,"顶部功能","消息")
            }
        }
    }


}