package com.ybmmarket20.common;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.AnimationDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.apkfuns.logutils.LogUtils;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.tencent.rmonitor.pagelaunch.PageLaunchMonitor;
import com.xyy.canary.utils.LogUtil;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.CommonH5Activity;
import com.ybmmarket20.activity.LoginActivity;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.UserRecallBean;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.common.eventbus.EventBusUtil;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.ConstantData;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StatusBarUtils;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.CommonDialogLayout;
import com.ybmmarket20.view.operationposition.ICountDown;
import com.ybmmarket20.viewmodel.BaseViewModel;
import com.ybmmarket20.viewmodel.UserRecallViewModel;
import com.ybmmarket20.xyyreport.spm.XyyReportActivity;
import com.ybmmarketkotlin.views.GrayWhiteSkinFrameLayout;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import butterknife.ButterKnife;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import ly.count.android.sdk.Countly;
import ly.count.android.sdk.XyyApmCly;
import qiu.niorgai.StatusBarCompat;

/**
 * Activity基类
 */
public abstract class BaseActivity extends XyyReportActivity implements ICountDown {
    public static final int CHECK_PERMISSION = 0x121;
    public static final int INSTALL_REQUEST = 0x122;
    public String merchant_id = "";
    public boolean isDestroy = false;
    public boolean isKaUser;//是否KA用户
    public RxPermissions rxPermissions;
    protected BroadcastReceiver br;
    protected BaseFlowData mFlowData; //埋点数据
    protected ViewDataBinding binding;
    String[] lastPermissions = null;
    int lastRequestPermissionsType = 0;//0代表requestEach，1代表requestEachCombined;
    PermissionCallBack lastCallBack;
    private AlertDialog dialog;
    private CommonDialogLayout dialogView;
    private InputMethodManager inputManager;
    private Animatable loadingDrawable;
    private int minSlop;
    private int lastAction;
    private float lastY;
    private boolean skinPeelerOn;//换肤开关
    private UserRecallViewModel vm;

    @SuppressLint("SourceLockedOrientationActivity")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        PageLaunchMonitor.getInstance().startSpan(this, this.getClass().getSimpleName(), "");
        PageLaunchMonitor.getInstance().startSpan(this, this.getClass().getSimpleName()+":::onCreate", this.getClass().getSimpleName());
        LogUtil.d("activity", "当前activity:" + this.getClass().getSimpleName());
        setObserver();
        skinPeelerOn = SpUtil.readBoolean(ConstantData.SKIN_PEELER, false);
        receiveAnalysisParams();
        getWindow().setBackgroundDrawable(null);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT); //设置不能横屏
        if (getContentViewId() > 0) {
//            setContentView(getContentViewId());

            binding = DataBindingUtil.setContentView(this, getContentViewId());

            ButterKnife.bind(this);
        }
        if (TextUtils.isEmpty(merchant_id)) {
            isLogin();
        }

        if (openDarkStatusMode()) {
            setTranslucentStatus();
        }
        if (!(this instanceof LoginActivity)) {
            br = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (intent != null && IntentCanst.ACTION_LOGOUT.equals(intent.getAction())) {
                        finish();
                    }
                }
            };
            LocalBroadcastManager.getInstance(getApplicationContext()).registerReceiver(br, new IntentFilter(IntentCanst.ACTION_LOGOUT));
        }
        //初始化是否KA用户
        isKaUser = SpUtil.isKa();
        initPermission();
        initHead();
        initCommon();
        initObserverBefore();
        initViewBefore();
        initData();
        if (isRegisterEventBus()) {
            EventBusUtil.register(this);
        }
//        vm = new ViewModelProvider(this).get(UserRecallViewModel.class);
//        vm.getUserRecallLiveData().observeForever(userRecallBeanBaseBean -> {
//            if (userRecallBeanBaseBean.isSuccess() && userRecallBeanBaseBean.data.isShow()) {
//                RoutersUtils.open(userRecallBeanBaseBean.data.getAction());
//            }
//        });
        PageLaunchMonitor.getInstance().endSpan(this, this.getClass().getSimpleName()+":::onCreate");

    }

    protected void initObserverBefore() {

    }

    protected void initViewBefore() {

    }

    private void setObserver() {
        BaseViewModel viewModel = getBaseViewModel();
        if (viewModel != null) {
            viewModel.getLoadingLiveData().observe(this, isShow -> {
                if (isShow) {
                    showProgress();
                } else {
                    dismissProgress();
                }
            });
        }
    }

    public void getUserCall() {
        RequestParams requestParams = new RequestParams();
        requestParams.put("merchantId", SpUtil.getMerchantid());
        HttpManager.getInstance().post(AppNetConfig.USER_CALL, requestParams, new BaseResponse<UserRecallBean>() {
            @Override
            public void onSuccess(String content, BaseBean<UserRecallBean> obj, UserRecallBean userRecallBean) {
                if (obj != null && obj.isSuccess() && userRecallBean.isShow()) {
                    RoutersUtils.open(userRecallBean.getAction());
                }
            }
        });
    }

    @Override
    public View onCreateView(String name, Context context, AttributeSet attrs) {//灰白换肤
        // activity的布局中所有view都会走这个创建方法，先找出content布局
        if (skinPeelerOn && name.equalsIgnoreCase("FrameLayout") && !(this instanceof CommonH5Activity)) {
            for (int i = 0; i < attrs.getAttributeCount(); i++) {
                // 获取到content布局的属性名id
                if (attrs.getAttributeName(i).equalsIgnoreCase("id")) {
                    // getAttributeValue(i)获取到的结果是 @2131296743， 需要转化成 int 值
                    int j = Integer.parseInt(attrs.getAttributeValue(i).substring(1));
                    // 根据id属性的 resId值，获取资源名，如
                    // com.ybmmarket20.debug:id/ll_root
                    // android:id/content
                    // 如果都匹配，说明是我们要找的布局文件，对此文件做替换
                    String resourceName = getResources().getResourceName(j);
                    LogUtils.tag("activity").e("Resourcename = " + resourceName);

                    if ("android:id/content".equalsIgnoreCase(resourceName)) {
                        return new GrayWhiteSkinFrameLayout(this, attrs);
                    }
                }
            }

        }
        return super.onCreateView(name, context, attrs);
    }

    @Override
    protected void onRestart() {
        isKaUser = SpUtil.isKa();
        super.onRestart();
    }

    /**
     * 权限申请方法，所在界面所有权限申请位置，方便查找。
     */
    protected void initPermission() {
        rxPermissions = new RxPermissions(this);
    }

    /**
     * 暴露给其他界面主动请求权限，每个请求的结果都会发送出来
     *
     * @param permissions
     */
    public void requestEachPermissions(PermissionCallBack callBack, String... permissions) {
        lastPermissions = permissions;
        lastRequestPermissionsType = 0;
        lastCallBack = callBack;
        Disposable subscribe = rxPermissions
                .requestEach(permissions)
                .subscribe(new Consumer<Permission>() {
                    @Override
                    public void accept(Permission permission) throws Exception {
                        if (permission.granted) {
                            callBack.granted(permission);
                        } else if (permission.shouldShowRequestPermissionRationale) {
                            callBack.showRequestPermissionRationale(permission);
                        } else {
                            callBack.denied(permission);
                        }
                    }
                });
    }

    /**
     * 暴露给其他界面主动请求权限，所有请求结果合并一个发送
     *
     * @param callBack
     * @param permissions
     */
    public void requestPermissionEachCombined(PermissionCallBack callBack, String... permissions) {
        lastPermissions = permissions;
        lastRequestPermissionsType = 1;
        lastCallBack = callBack;
        Disposable subscribe = rxPermissions
                .requestEachCombined(permissions)
                .subscribe(new Consumer<Permission>() {
                    @Override
                    public void accept(Permission permission) throws Exception {
                        if (permission.granted) {
                            callBack.granted(permission);
                        } else if (permission.shouldShowRequestPermissionRationale) {
                            callBack.showRequestPermissionRationale(permission);
                        } else {
                            callBack.denied(permission);
                        }
                    }
                });
    }

    /*初始化头部*/
    protected void initHead() {
    }

    /*设置界面视图*/
    protected abstract int getContentViewId();

    /*初始化数据*/
    protected abstract void initData();

    /**
     * 是否开启黑色状态栏字体颜色
     *
     * @return
     */
    public boolean openDarkStatusMode() {
        return true;
    }

    protected void initCommon() {
        View view = getView(R.id.iv_back);//通用返回
        if (view != null) {
            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    finish();
                    startMainPage();
                }
            });
        }
        View llTitle = findViewById(R.id.ll_title);
        YBMAppLike.changeThemeBg(R.drawable.base_header_dynamic_bg, llTitle);
    }

    //设置标题
    public void setTitle(String str) {
        try {
            TextView view = getView(R.id.tv_title);//通用标题
            if (view != null) {
                view.setText(str);
            }
        } catch (Throwable e) {
            LogUtils.d(e.getClass());
            e.printStackTrace();
            finish();
        }
    }

    public TextView getTitleText() {
        return getView(R.id.tv_title);
    }

    //设置右边标题
    public void setRigthText(View.OnClickListener listener, int resId) {
        try {
            setRigthText(listener, getResources().getString(resId));
        } catch (Throwable e) {
            LogUtils.d(e.getClass());
            finish();
        }
    }

    public TextView getRigthText() {
        return getView(R.id.tv_right);
    }

    //设置右边标题
    public void setRigthText(String text) {
        setRigthText(null, text);
    }

    //设置右边标题
    public void setRigthText(View.OnClickListener listener, String text) {
        try {
            View view = getView(R.id.tv_right);//通用标题
            if (view != null) {
                if (view instanceof TextView) {
                    TextView iv = (TextView) view;
                    iv.setText(text);
                }
                view.setVisibility(View.VISIBLE);
                view.setOnClickListener(listener);
            }

        } catch (Throwable e) {
            LogUtils.d(e.getClass());
            e.printStackTrace();
            finish();
        }
    }

    //设置右边图标
    public void setRigthImg(View.OnClickListener listener, int resId) {
        try {
            View view = getView(R.id.iv_right);//通用标题
            if (view != null) {
                if (resId > 0) {
                    if (view instanceof ImageView) {
                        ImageView iv = (ImageView) view;
                        iv.setImageResource(resId);
                    } else if (view instanceof TextView) {
                        TextView iv = (TextView) view;
                        iv.setText(getResources().getString(resId));
                    }
                }
                view.setVisibility(View.VISIBLE);
                view.setOnClickListener(listener);
            }

        } catch (Throwable e) {
            LogUtils.d(e.getClass());
            e.printStackTrace();
            finish();
        }
    }

    //设置左边图标
    public void setLeft(View.OnClickListener listener, int resId) {
        try {
            View view = getView(R.id.iv_back);//通用标题
            if (view != null) {
                if (resId > 0) {
                    if (view instanceof ImageView) {
                        ImageView iv = (ImageView) view;
                        iv.setImageResource(resId);
                    } else if (view instanceof TextView) {
                        TextView iv = (TextView) view;
                        iv.setText(getResources().getString(resId));
                    }
                }
                view.setVisibility(View.VISIBLE);
                if (listener != null) {
                    view.setOnClickListener(listener);
                }
            }

        } catch (Throwable e) {
            LogUtils.d(e.getClass());
            e.printStackTrace();
            finish();
        }
    }

    //设置左边图标
    public void setLeft(View.OnClickListener listener) {
        setLeft(listener, -1);
    }

    //设置状态(不合法的state不处理)
    public void setLeftVisibility(int state) {
        View leftView = getView(R.id.iv_back);
        switch (state) {
            case View.VISIBLE:
                leftView.setVisibility(View.VISIBLE);
                break;
            case View.INVISIBLE:
                leftView.setVisibility(View.INVISIBLE);
                break;
            case View.GONE:
                leftView.setVisibility(View.GONE);
                break;
        }
    }

    @TargetApi(19)
    private void setTranslucentStatus() {
        StatusBarCompat.translucentStatusBar(this, true);
        StatusBarUtils.setLightMode(this);
    }

    /**
     * 跳转activity
     *
     * @param clazz
     * @param bundle
     */
    public void gotoAtivity(Class clazz, Bundle bundle) {
        Intent it = new Intent(this, clazz);
        if (bundle != null) {
            it.putExtra("bundle", bundle);
        }
        startActivity(it);
    }

    public <T extends View> T getView(int viewId) {
        try {
            return (T) findViewById(viewId);
        } catch (Throwable e) {
            return null;
        }
    }

    /**
     * 跳转activity
     *
     * @param clazz
     */
    public void gotoAtivity(Class clazz) {
        gotoAtivity(clazz, null);
    }

    public boolean isLogin() {
        merchant_id = SpUtil.getMerchantid();
        if (!TextUtils.isEmpty(merchant_id)) {
            return true;
        }
        return false;
    }

    /**
     * 清除登录信息
     */
    public void loginOut() {
        SpUtil.clearMerchantid();

        // 退出后更新一下ApmCly 的 Session
        if (Countly.sharedInstance().isInitialized()) {
            XyyApmCly.getInstance().logout();
        }

        //清空密码提示强度提示
        SpUtil.writeBoolean("already_mention", false);
        merchant_id = null;
        //退出广播
        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_LOGOUT));
    }

    /**
     * 是否正在loading
     *
     * @return
     */
    public boolean isShowingProgress() {
        if (dialog == null) return false;
        return dialog.isShowing();
    }

    //显示加载对话框
    public void showProgress(String msg, boolean showMsg, final boolean cancelable) {
        if (dialog == null) {
            AlertDialog.Builder builder = new AlertDialog.Builder(this, R.style.AlertDialog);
            builder.setCancelable(false);
            dialogView = new CommonDialogLayout(this);
            ImageView iv = dialogView.getImageView();
            if (iv == null) {
                iv = new ImageView(this);
            }
            iv.setImageResource(R.drawable.loading);
            loadingDrawable = (AnimationDrawable) iv.getDrawable();
            dialog = builder.create();
            if (dialog != null) {
                dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
                    @Override
                    public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                        if (keyCode == KeyEvent.KEYCODE_APP_SWITCH || keyCode == KeyEvent.KEYCODE_SEARCH || keyCode == KeyEvent.KEYCODE_BACK || keyCode == KeyEvent.KEYCODE_MENU || keyCode ==
                                KeyEvent.KEYCODE_BACKSLASH) {
                            return !cancelable;
                        }
                        return false;
                    }
                });
            }
        } else {
            if (this.isFinishing() || dialog.getOwnerActivity() == null || dialog.getOwnerActivity() != this) {
                try {
                    dialog.dismiss();
                    if (loadingDrawable != null) {
                        loadingDrawable.stop();
                    }
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    dialog = null;
                    showProgress(msg, showMsg, cancelable);
                }
            }
        }
        if (TextUtils.isEmpty(msg)) {
            showMsg = false;
        }
        //        showMsg = false;//强制不显示文字
        dialogView.getTextView().setVisibility(showMsg ? View.VISIBLE : View.GONE);
        if (showMsg) {
            dialogView.setMsg(msg);
        }

        dialog.setCanceledOnTouchOutside(cancelable);
        try {
            dialog.show();
            loadingDrawable.start();
            dialog.getWindow().setContentView(dialogView);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.width = ConvertUtils.dp2px(100);
            params.height = ConvertUtils.dp2px(100);
            params.dimAmount = 0.01f;
            dialog.getWindow().setAttributes(params);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            dialog = null;
        }
    }

    public void showProgress() {
        showProgress("加载中", true, true);
    }

    public void showProgress(boolean cancelable) {
        showProgress("加载中", true, cancelable);
    }

    public void showProgress(String msg) {
        showProgress(msg, true, true);
    }

    //取消对话框
    public void dismissProgress() {
        if (dialog != null && dialog.isShowing() && dialog.getOwnerActivity() != this) {
            try {
                dialog.dismiss();
                if (loadingDrawable != null) {
                    loadingDrawable.stop();
                }
            } catch (Throwable throwable) {
                throwable.printStackTrace();
                dialog = null;
            }
        }
    }

    // 隐藏软键盘
    public void hideSoftInput() {
        try {
            if (inputManager == null) {
                inputManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            }
            if (getCurrentFocus() == null || getCurrentFocus().getWindowToken() == null) {
                inputManager.hideSoftInputFromWindow(getWindow().getDecorView().getWindowToken(), 0);
            } else {
                inputManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
            }
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
    }

    public void hideSoftInput(View view) {
        if (view == null) {
            return;
        }
        try {
            if (inputManager == null) {
                inputManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            }
            inputManager.hideSoftInputFromWindow(view.getWindowToken(), 0);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public void hideSoftInput(View... views) {
        for (View v : views) {
            hideSoftInput(v);
        }
    }

    // 隐藏软键盘
    public void showSoftInput() {
        try {
            if (inputManager == null) {
                inputManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            }
            inputManager.showSoftInput(getCurrentFocus(), InputMethodManager.SHOW_IMPLICIT);
        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    private void reportLaunchFinished() {
        PageLaunchMonitor.getInstance().reportActivityFullLaunch(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        PageLaunchMonitor.getInstance().startSpan(this, this.getClass().getSimpleName()+":::onResume", this.getClass().getSimpleName());

        // 等页面加载完成之后，才调用 reportLaunchFinished 来定义Activity启动结束
        new Thread(new Runnable() {
            @Override
            public void run() {
                reportLaunchFinished();

            }
        }).start();
        XyyIoUtil.startTrack(this);
        if (getApplication() instanceof YBMAppLike) {
            if (((YBMAppLike) getApplication()).isShowCallUser && PrivacyInitManager.INSTANCE.isAgreedPrivacy()) {
                getUserCall();
            }
        }
        PageLaunchMonitor.getInstance().endSpan(this, this.getClass().getSimpleName()+":::onResume");
        PageLaunchMonitor.getInstance().endSpan(this, this.getClass().getSimpleName());
    }

    @Override
    protected void onPause() {
        super.onPause();
        XyyIoUtil.endTrack(this);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        //从设置界面回来后重新检查一次权限
        if (requestCode == CHECK_PERMISSION) {
            if (lastCallBack != null) {
                if (lastRequestPermissionsType == 0) {
                    requestEachPermissions(lastCallBack, lastPermissions);
                } else {
                    requestPermissionEachCombined(lastCallBack, lastPermissions);
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        isDestroy = true;
        dismissProgress();
        super.onDestroy();
        XyyIoUtil.flush();
        ButterKnife.unbind(this);
        if (br != null) {
            LocalBroadcastManager.getInstance(getApplicationContext()).unregisterReceiver(br);
        }

        if (isRegisterEventBus()) {
            EventBusUtil.unregister(this);
        }
        lastCallBack = null;
    }

    @Override
    public boolean isFinishing() {
        return super.isFinishing() || isDestroy;
    }

    @Override
    public void finish() {
        isDestroy = true;
        dismissProgress();
        hideSoftInput();
        super.finish();
    }

    //返回自己
    public BaseActivity getMySelf() {
        return BaseActivity.this;
    }

    @Override
    public void onBackPressed() {
        startMainPage();
        super.onBackPressed();
    }

    private void startMainPage() {
        if (!(this instanceof MainActivity) && isLogin() && (MainActivity.getMainActivity() == null || MainActivity.getMainActivity().isFinishing())) {//开启页面
            startActivity(MainActivity.getIntent2Me(true));
        }
    }

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onSaveInstanceState(Bundle outState) {//此处勿动 还原此处空实现，修复引起的页面崩溃重新加载组件空指针的问题
    }

    protected boolean isScroll(MotionEvent ev) {
        if (minSlop <= 0) {
            minSlop = ViewConfiguration.get(this).getScaledTouchSlop();
        }
        if (ev.getAction() != MotionEvent.ACTION_MOVE || lastAction != MotionEvent.ACTION_MOVE || (Math.abs(ev.getRawY() - lastY) < minSlop)) {
            lastAction = ev.getAction();
            lastY = ev.getRawY();
            return false;
        }
        lastAction = ev.getAction();
        lastY = ev.getRawY();
        hideSoftInput();
        return true;
    }

    /**
     * 是否注册事件分发
     *
     * @return true绑定EventBus事件分发，默认不绑定，子类需要绑定的话复写此方法返回true.
     */
    protected boolean isRegisterEventBus() {
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventBusCome(Event event) {
        if (event != null) {
            receiveEvent(event);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onStickyEventBusCome(Event event) {
        if (event != null) {
            receiveStickyEvent(event);
        }
    }

    /**
     * 接收到分发到事件
     *
     * @param event 事件
     */
    protected void receiveEvent(Event event) {

    }

    /**
     * 接受到分发的粘性事件
     *
     * @param event 粘性事件
     */
    protected void receiveStickyEvent(Event event) {

    }

    public String getPageName() {
        return "";
    }

    /**
     * 开启带埋点参数的路由
     *
     * @param url
     */
    protected void openUrl(String url) {
        FlowDataAnalysisManagerKt.openUrl(url, mFlowData);
    }

    /**
     * 接收埋点参数
     */
    protected void receiveAnalysisParams() {
        mFlowData = FlowDataAnalysisManagerKt.generateDefaultBaseFlowData();
        FlowDataAnalysisManagerKt.receiveAnalysisParams(this, mFlowData);
    }

    protected BaseViewModel getBaseViewModel() {
        return null;
    }

    @Nullable
    @Override
    public SparseArray<CountDownTimer> getCacheMap() {
        return null;
    }

    public interface Scroll {
        void scroll2Position(int position);
    }

    /**
     * 权限回调方法
     */
    public abstract static class PermissionCallBack {
        //权限拒绝提示语
        public String refuseMention;
        public boolean go2settings = true;

        public PermissionCallBack() {

        }

        public PermissionCallBack(String refuseMention) {
            this.refuseMention = refuseMention;
        }

        public PermissionCallBack(String refuseMention, boolean go2settings) {
            this.refuseMention = refuseMention;
            this.go2settings = go2settings;
        }

        public abstract void granted(Permission permission);

        public void showRequestPermissionRationale(Permission permission) {
            if (go2settings) {
                gotoSettings(permission);
            }
        }

        public void denied(Permission permission) {
            if (go2settings) {
                gotoSettings(permission);
            }
        }

        /**
         * 跳转设置方法
         *
         * @param permission
         */
        private void gotoSettings(Permission permission) {
            Activity activity = YBMAppLike.getApp().getCurrActivity();
            AlertDialog.Builder dialog = new AlertDialog.Builder(activity);
            dialog.setNegativeButton("取消", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            });
            dialog.setPositiveButton("去设置", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    Uri uri = Uri.fromParts("package", YBMAppLike.getApp().getCurrActivity().getPackageName(), null);
                    intent.setData(uri);
                    try {
                        YBMAppLike.getApp().getCurrActivity().startActivityForResult(intent, CHECK_PERMISSION);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
            dialog.setTitle("权限设置");
            dialog.setMessage(!TextUtils.isEmpty(refuseMention) ? refuseMention : permission.name);
            if (isLiving(activity)) {//解决BadTokenException
                dialog.show();
            }
        }

        private boolean isLiving(Activity activity) {
            if (activity == null) {
                return false;
            }
            if (activity.isFinishing()) {
                return false;
            }
            return true;
        }
    }
}
