package com.ybmmarket20.adapter

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.ClerkAptitudeAuthenticationActivity
import com.ybmmarket20.bean.ClerkAptitudeAuthenticationLayoutData
import com.ybmmarket20.view.WrapContentLinearLayoutManager
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 店员资质认证
 */
class ClerkAptitudeAuthenticationAdapter(data: MutableList<ClerkAptitudeAuthenticationLayoutData>) :
    YBMBaseAdapter<ClerkAptitudeAuthenticationLayoutData>(R.layout.item_clerk_aptitude_authentication, data) {

    override fun bindItemView(
        baseViewHolder: YBMBaseHolder?,
        t: ClerkAptitudeAuthenticationLayoutData?
    ) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            val tvTitle = holder.getView<TextView>(R.id.tv_title)
            val titleBuilder = SpannableStringBuilder(bean.title)
            if (bean.clickableText != null) {
                val titleClickableBuilder = SpannableStringBuilder(bean.clickableText)
                titleClickableBuilder.setSpan(ForegroundColorSpan(ContextCompat.getColor(mContext, R.color.base_color)), 0, titleClickableBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                titleBuilder.append(titleClickableBuilder)
            }
            tvTitle.text = titleBuilder

            //设置横向列表
            val rv = holder.getView<RecyclerView>(R.id.rv)
            rv.layoutManager = WrapLinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
            val imageList = bean.imageList.map {
                val imageInfo = LicensePicListAdapter.ImageInfo()
                imageInfo.oldPath = it
                imageInfo
            }.toMutableList()
            if (imageList.isEmpty() || imageList.size < 3) {
                val imageInfo = LicensePicListAdapter.ImageInfo()
                imageInfo.localPath = LicensePicListAdapter.EDIT_FLAG
                imageList.add(imageInfo)
            }
            val itemAdapter = LicensePicListAdapter(R.layout.item_image, imageList, true, WrapContentLinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false))
            itemAdapter.setMaxSize(3)
            if (mContext is ClerkAptitudeAuthenticationActivity) {
                itemAdapter.setListener(mContext as ClerkAptitudeAuthenticationActivity)
            }
            rv.adapter = itemAdapter


        }
    }

}