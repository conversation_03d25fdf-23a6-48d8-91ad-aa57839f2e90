package com.ybmmarket20.adapter

import android.text.SpannableStringBuilder
import android.view.View
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.FlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.getPriceStringBuilder1
import com.ybmmarket20.view.homesteady.whenAllNotNull
import org.json.JSONObject

/**
 * 拼团启动页热卖排行榜商品adapter
 */
class SearchStartRecommendGoodsAdapter(
    data: List<RowsBean>,
    val type: Int = 2, //榜单类型:1-拼团榜单，2-品类榜单
    val flowData: FlowData? = null,
    val categoryLink: String?,
    val categoryName: String? = ""
) : YBMBaseAdapter<RowsBean>(R.layout.item_search_start_recommend_goods, data) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: RowsBean?) {
        whenAllNotNull(baseViewHolder, t) { holder, row ->
            //拼团进行中商品商品图显示边框、拼团价、已拼数量
            if (type == 1) {
                //拼团进行中
                holder.setVisible(R.id.group_search_start_recommend_goods_spellgroup, true)
                holder.setVisibleOrInvisible(
                    R.id.tv_search_start_recommend_goods_spellgroup_count,
                    true
                )
                holder.setText(
                    R.id.tv_search_start_recommend_goods_spellgroup_count,
                    row.actPt?.merchantCountDesc ?: ""
                )
                val span = SpannableStringBuilder(if(row.actPt?.isStepPrice() == true) "" else "拼团价")
                span.append(
                    getPriceStringBuilder1(
                        if(row.actPt?.isStepPrice() == true) {
                            UiUtils.transform(row.actPt?.minSkuPrice?: "0.00")
                        } else {
                            StringUtil.DecimalFormat2Double(
                                row.actPt?.assemblePrice ?: 0.00
                            )
                        }

                    )
                )
                if(row.actPt?.isStepPrice() == true) span.append("起")
                holder.getView<TextView>(R.id.tv_search_start_recommend_goods_spellgroup_price).text =
                    span
            } else {
                holder.setVisible(R.id.group_search_start_recommend_goods_spellgroup, false)
                holder.setVisibleOrInvisible(
                    R.id.tv_search_start_recommend_goods_spellgroup_count,
                    false
                )
            }
            holder.getView<TextView>(R.id.tv_search_start_recommend_goods_title).text = t?.showName
            //前三带排名标签
            val goodsTag = holder.getView<TextView>(R.id.tv_search_start_recommend_goods_tag)
            if (holder.layoutPosition <= 2) {
                goodsTag.visibility = View.VISIBLE
                goodsTag.text = "${holder.layoutPosition + 1}"
                goodsTag.setBackgroundResource(
                    when (holder.layoutPosition) {
                        0 -> R.drawable.icon_search_start_goods_first
                        1 -> R.drawable.icon_search_start_goods_second
                        2 -> R.drawable.icon_search_start_goods_third
                        else -> 0
                    }
                )
            } else {
                goodsTag.visibility = View.GONE
            }
            //规格
            holder.setText(R.id.tv_search_start_recommend_goods_spec, row.spec)
            ImageUtil.load(
                mContext,
                AppNetConfig.LORD_IMAGE + row.imageUrl,
                holder.getView(R.id.iv_search_start_recommend_goods)
            )
            var action = row.jumpUrl ?: categoryLink
            action =
                "$action&sptype=${flowData?.spType}&spid=${flowData?.spId}&sid=${flowData?.sId}"
            holder.itemView.setOnClickListener {
                RoutersUtils.open(action)
                XyyIoUtil.track("action_SearchStartup_chartCommodity_Click", JSONObject().also {
                    it.put("sptype", flowData?.spType ?: "")
                    it.put("spid", flowData?.spId ?: "")
                    it.put("sid", flowData?.sId ?: "")
                    it.put("action", action)
                    it.put("productId", row.productId)
                    it.put("categoryName", categoryName)
                })
                XyyIoUtil.checkSpTypeField(flowData, true)
            }
            XyyIoUtil.track("action_SearchStartup_chartCommodity_Exposure", JSONObject().also {
                it.put("productId", row.productId)
                it.put("categoryName", categoryName)
            })
        }
    }
}