package com.ybmmarket20.adapter;

import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybmmarket20.bean.ExpandableItem;

import java.util.ArrayList;
import java.util.List;

/**
 * 分层adapter
 */

public abstract class YBMGroupListAdapter<T extends ExpandableItem> extends YBMBaseMultiItemAdapter<T> {


    public YBMGroupListAdapter(int group, int content, List<T> data) {
        super(data);
        addItemType(ExpandableItem.TYPE_CONTENT, content);
        addItemType(ExpandableItem.TYPE_GROUP, group);
    }


    protected final void bindItemView(YBMBaseHolder ybmBaseHolder, T object) {
        if (object.isGroup()) {
            bindGroupView(ybmBaseHolder,object);
        } else {
            bindContentView(ybmBaseHolder, object);
        }
    }

    public void expand_(int position) {
        expand_(position, true);
    }

    public void expand_(int position, boolean animate) {
        ExpandableItem expandable = getExpandableItem(position);
        if (expandable == null || expandable.isExpanded()) {
            return;
        }
        int subItemCount = 0;
        if (!expandable.isExpanded()) {//删除全部子item
            List<T> subItems = expandable.getSubItems();
            subItemCount = subItems.size();
            mData.addAll(position + 1, subItems);
        }
        expandable.setExpanded(true);
        if (animate) {
            notifyItemChanged(position);
            notifyItemRangeInserted(position + 1, subItemCount);
        } else {
            notifyDataSetChanged();
        }
    }

    public void collapse_(int position) {
        collapse_(position, true);
    }

    public void collapse_(int position, boolean animate) {
        ExpandableItem expandable = getExpandableItem(position);
        if (expandable == null || !expandable.isExpanded()) {
            return;
        }
        int subItemCount = 0;
        if (expandable.isExpanded()) {//删除全部子item
            List<T> subItems = expandable.getSubItems();
            for (int i = subItems.size() - 1; i >= 0; i--) {
                T subItem = subItems.get(i);
                int pos = getItemPosition(subItem);
                if (pos < 0) {
                    continue;
                }
                subItemCount++;
                mData.remove(pos);
            }
        }
        expandable.setExpanded(false);
        if (animate) {
            notifyItemChanged(position);
            notifyItemRangeRemoved(position + 1, subItemCount);
        } else {
            notifyDataSetChanged();
        }
    }

    private void expand_(T item) {
        int position = getItemPosition(item);
        if (position < 0) {
            return;
        }
        ExpandableItem expandable = getExpandableItem(position);
        if (expandable == null || expandable.isExpanded()) {
            return;
        }
        if (!expandable.isExpanded()) {//添加全部子item
            List<T> subItems = expandable.getSubItems();
            mData.addAll(position + 1, subItems);
        }
        expandable.setExpanded(true);
    }

    private ExpandableItem getExpandableItem(int position) {
        if (position != -1 && position < mData.size()) {
            Object obj = mData.get(position);
            if (obj != null && obj instanceof ExpandableItem) {
                return (ExpandableItem) obj;
            }
        }
        return null;
    }

    private int getItemPosition(T item) {
        return item != null && mData != null && !mData.isEmpty() ? mData.indexOf(item) : -1;
    }

    public void notifyGroupDataChanged(List<T> listGroup) {
        List<T> temp = new ArrayList<>();
        for (int a = 0; a < listGroup.size(); a++) {
            if (listGroup.get(a) != null && listGroup.get(a).isGroup()) {
                temp.add(listGroup.get(a));
            }
        }
        if (mData == null) {
            mData = new ArrayList();
        }
        mData.clear();
        mData.addAll(temp);
        for (int a = 0; a < temp.size(); a++) {
            if (temp.get(a) != null && temp.get(a).isGroup() && temp.get(a).isExpanded()) {
                temp.get(a).setExpanded(false);
                expand_(temp.get(a));
            }
        }
        notifyDataSetChanged();
    }

    public abstract void bindGroupView(YBMBaseHolder ybmBaseHolder, T t);


    public abstract void bindContentView(YBMBaseHolder ybmBaseHolder, T t);


}
