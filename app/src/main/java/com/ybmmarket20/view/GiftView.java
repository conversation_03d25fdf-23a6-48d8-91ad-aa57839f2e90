package com.ybmmarket20.view;

import android.content.Context;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.GiftSkulist;
import com.ybmmarket20.common.util.ConvertUtils;

import java.util.List;

public class GiftView extends LinearLayout {

    private int status;

    public GiftView(Context context) {
        this(context, null);
    }

    public GiftView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        setOrientation(VERTICAL);
        setGravity(Gravity.CENTER_VERTICAL | Gravity.LEFT);
    }

    /**
     * 绑定数据
     *
     * @param list 标签组
     *             默认三个，
     */
    public void bindData(List<GiftSkulist> list) {
        bindData(list, 3);
    }

    /**
     * 绑定数据
     *
     * @param list 标签组
     * @param max  最多显示多少个，<=0 或者3 都用默认三个，
     */
    public void bindData(List<GiftSkulist> list, int max, int status, boolean gone) {
        removeAllViews();
        if (list == null || list.size() <= 0) {//添加一个空view,填充高度问题
            setVisibility(gone ? View.GONE : View.INVISIBLE);
            addView(createView(null));
            return;
        } else {
            setVisibility(View.VISIBLE);
        }
        if (max <= 0) {
            max = 3;
        }
        int size = Math.min(max, list.size());
        for (int a = 0; a < size; a++) {
            addView(createView(list.get(a)));
        }
    }

    public void bindData(List<GiftSkulist> list, int max) {
        bindData(list, max, 1, false);
    }

    /**
     * 绑定数据
     *
     * @param list 标签组
     * @param max  最多显示多少个，<=0 或者3 都用默认三个，
     */
    public void bindData(List<GiftSkulist> list, int max, int status) {
        this.status = status;
        bindData(list, max, status, false);
    }

    private LinearLayout createView(GiftSkulist bean) {

        LinearLayout view = new LinearLayout(getContext());
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        view.setLayoutParams(params);//设置布局参数
        view.setOrientation(LinearLayout.HORIZONTAL);// 设置子View的Linearlayout// 为垂直方向布局

        LayoutParams lp = new LayoutParams(0, ConvertUtils.dp2px(25));
        lp.weight = 1;
        TextView textViewName = new TextView(getContext());
        textViewName.setLayoutParams(lp);
        textViewName.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.bg_bank_tips), null, null, null);
        textViewName.setCompoundDrawablePadding(ConvertUtils.dp2px(7));
        textViewName.setPadding(ConvertUtils.dp2px(1), 0, ConvertUtils.dp2px(1), 0);
        textViewName.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        textViewName.setTextColor(getResources().getColor(setColors(status)));
        textViewName.setSingleLine();
        textViewName.setMaxLines(1);
        textViewName.setEllipsize(TextUtils.TruncateAt.END);
        textViewName.setText(bean.showName);

        lp = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ConvertUtils.dp2px(25));
        TextView textViewCount = new TextView(getContext());
        textViewCount.setLayoutParams(lp);
        textViewCount.setPadding(ConvertUtils.dp2px(1), 0, ConvertUtils.dp2px(1), 0);
        textViewCount.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        textViewCount.setTextColor(getResources().getColor(setColors(status)));

        textViewCount.setText(bean.skuCount + bean.productUnit + "");

        view.addView(textViewName);//将TextView 添加到子View 中
        view.addView(textViewCount);//将TextView 添加到子View 中

        return view;
    }

    /*
     * 设置颜色
     * */
    public int setColors(int status) {

        int colorRes = R.color.text_292933;

        switch (status) {
            case 1:
            default:
                colorRes = R.color.text_292933;
                break;
            case 2:
            case 3:
            case 4:
                colorRes = R.color.text_676773;
                break;
        }
        return colorRes;
    }

}
