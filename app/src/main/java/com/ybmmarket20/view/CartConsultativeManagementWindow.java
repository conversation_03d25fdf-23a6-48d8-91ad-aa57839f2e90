package com.ybmmarket20.view;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CartVoucher;
import com.ybmmarket20.bean.VoucherListBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.BitmapUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

public class CartConsultativeManagementWindow extends BaseBottomPopWindow {

    private YBMBaseAdapter<VoucherListBean> mAdapter;
    private CommonRecyclerView mList;
    private int pagesSize = 9999;
    private List<VoucherListBean> mDatas = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.cart_consultative_canagement_pop;
    }

    @Override
    protected LinearLayout.LayoutParams getLayoutParams() {
        return new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, UiUtils.getScreenHeight()- ConvertUtils.dp2px(120));
    }

    @Override
    protected void initView() {
        ImageView ivClose = getView(R.id.iv_close);
        ivClose.setImageDrawable(BitmapUtil.tintDrawable(UiUtils.getDrawable(contentView.getContext(),R.drawable.icon_close),UiUtils.getColor(R.color.text_292933)));
        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mList = getView(R.id.crv_list);
        contentView.findViewById(R.id.bg).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        mList.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getData();
            }

            @Override
            public void onLoadMore() {

            }
        });

        mAdapter = new YBMBaseAdapter<VoucherListBean>(R.layout.cart_consultative_management_item, mDatas) {
            @Override
            protected void bindItemView(final YBMBaseHolder holder, final VoucherListBean bean) {

            }
        };
        mList.setAdapter(mAdapter);
        mList.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty_coupon, "暂无数据");
        mAdapter.setEnableLoadMore(false);
    }

    private void completion() {
        if (mList != null) {
            mList.setRefreshing(false);
        }
    }

    public RequestParams getParams() {

        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.setUrl(AppNetConfig.SELECT_CART_VOUCHER);
        params.put("merchantId", merchantid);
        params.put("offset", 0+"");
        params.put("limit", pagesSize+"");

        return params;
    }

    public void getData() {

        HttpManager.getInstance().post(getParams(), new BaseResponse<CartVoucher>() {

            @Override
            public void onSuccess(String content, BaseBean<CartVoucher> obj, CartVoucher data) {
                completion();
                if (obj != null && obj.isSuccess() && data !=null && data.getList() !=null) {
                    mDatas.clear();
                    mDatas.addAll(data.getList());
                    mAdapter.setNewData(mDatas);
                }else {
                    mAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                mAdapter.notifyDataSetChanged();
            }
        });
    }

    /**
     * @param list
     */
    public void setNewData(List<VoucherListBean> list) {
        this.mDatas = list;
        if (mAdapter != null) {
            mAdapter.setNewData(list);
        }
    }

}
