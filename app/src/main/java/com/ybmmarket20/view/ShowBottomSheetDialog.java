package com.ybmmarket20.view;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.utils.RoutersUtils;

/**
 * 通用底部弹出dialog
 */

public class ShowBottomSheetDialog extends BaseShowBottomSheetDialog {

    private String mobile;
    private TextView tvSalesman;
    private TextView tvService;
    private int num;

    public ShowBottomSheetDialog(Context context) {
        super(context);
    }

    public void initData(String mobile, int num) {
        this.mobile = mobile;
        this.num = num;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.dialog_layout_base_item;
    }

    @Override
    protected void initView() {

        tvSalesman = getView(R.id.tv_salesman);
        tvService = getView(R.id.tv_service);

        tvSalesman.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RoutersUtils.telPhone(false,mobile);
                dismiss();
            }
        });

        tvService.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RoutersUtils.telKefu(false);
                dismiss();
            }
        });

    }

    @Override
    protected int getListH() {
        return 0;
    }

    @Override
    protected LinearLayout.LayoutParams getLayoutParams() {
        return new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }
}
