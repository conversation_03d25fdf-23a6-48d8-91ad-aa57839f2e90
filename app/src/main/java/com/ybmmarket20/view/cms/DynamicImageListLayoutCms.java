package com.ybmmarket20.view.cms;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.cms.ModuleBeanCms;
import com.ybmmarket20.bean.cms.ModuleItemBannerBean;
import com.ybmmarket20.utils.analysis.XyyIoUtil;

import java.util.List;

/**
 * 横向滑动的图片列表
 */
public class DynamicImageListLayoutCms extends BaseDynamicLayoutCms<ModuleItemBannerBean>  {

    protected RecyclerView listView;
    protected YBMBaseAdapter adapter;

    public DynamicImageListLayoutCms(Context context) {
        super(context);
    }

    public DynamicImageListLayoutCms(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicImageListLayoutCms(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        listView = (RecyclerView) findViewById(R.id.rvc_list);
        listView.setNestedScrollingEnabled(false);
    }

    @Override
    public boolean supportSetHei() {
        return true;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_image_list;
    }

    @Override
    public boolean needUpdateItem(ModuleBeanCms<ModuleItemBannerBean> moduleBean, List<ModuleItemBannerBean> items) {
        return true;
    }

    @Override
    public void setItemData(ModuleBeanCms moduleBean, List<ModuleItemBannerBean> items, boolean isUpdate) {
        if (adapter == null) {
            adapter = new YBMBaseAdapter<ModuleItemBannerBean>(R.layout.dynamic_layout_image_list_item, items) {
                @Override
                protected void bindItemView(YBMBaseHolder baseViewHolder, ModuleItemBannerBean moduleViewItem) {
                    ImageView iv = baseViewHolder.getView(R.id.iv);
                    setImageView(iv, moduleViewItem);
                    // cms 多图广告位埋点
                    iv.setTag(R.id.tag_action, moduleViewItem.action);
                    iv.setTag(R.id.tag_2, baseViewHolder.getAdapterPosition());
                    iv.setTag(R.id.tag_click_type, XyyIoUtil.ACTION_HOME_MAIN_AD);
                    iv.setOnClickListener(itemClick);
                }
            };
            listView.setLayoutManager(new WrapLinearLayoutManager(getContext(), HORIZONTAL, false));
            listView.setAdapter(adapter);
        } else {
            adapter.setNewData(items);
        }
    }

    @Override
    public void setStyle(int style) {
        if (style <= 0) {
            return;
        }

    }

    @Override
    public void setImageView(ImageView view, ModuleItemBannerBean bean) {
        try {
            if (TextUtils.isEmpty(bean.image)) {
                return;
            } else {
                ImageHelper.with(getContext()).load(getImgUrl(bean.image)).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(view);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }
}
