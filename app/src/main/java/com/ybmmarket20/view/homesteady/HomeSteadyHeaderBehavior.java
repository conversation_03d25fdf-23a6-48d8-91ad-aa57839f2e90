package com.ybmmarket20.view.homesteady;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import com.flyco.tablayout.SlidingHomeSteadyTabLayout;
import com.google.android.material.appbar.AppBarLayout;
import com.luck.picture.lib.tools.ScreenUtils;
import com.ybmmarket20.R;

import java.lang.reflect.Method;

/**
 * 处理tab折叠
 */
public class HomeSteadyHeaderBehavior extends AppBarLayout.Behavior {

    private int scaleHeight = 0;
    private int maxTabLayoutHeight = 0;
    private int minTabLayoutHeight = 0;
    private int currTabLayoutHeight = 0;
    private SlidingHomeSteadyTabLayout tabLayout = null;

    public HomeSteadyHeaderBehavior(Context context, AttributeSet attrs) {
        super(context, attrs);
        scaleHeight = ScreenUtils.dip2px(context, 17);
        maxTabLayoutHeight = ScreenUtils.dip2px(context, 50);
        minTabLayoutHeight = maxTabLayoutHeight - scaleHeight;
        currTabLayoutHeight = maxTabLayoutHeight;
    }

    @Override
    public void onNestedPreScroll(CoordinatorLayout coordinatorLayout, AppBarLayout child, View target, int dx, int dy, int[] consumed, int type) {
//        Log.i("onNestedPreScroll", "dy = " + dy + "  " + "counsumedDy = " + consumed[1]);
        if (isTop(coordinatorLayout, child) && currTabLayoutHeight > minTabLayoutHeight && dy > 0) {
            // tab吸顶后继续向上滑动处理折叠
            int min;
            int max;
            min = -invokeVoidMethod(child, "getUpNestedPreScrollRange");
            max = 0;
            if (min != max) {
                consumed[1] = dy;
                handleTabHeight(child, dy);
            }
        } else {
            super.onNestedPreScroll(coordinatorLayout, child, target, dx, dy, consumed, type);
        }
    }

    @Override
    public void onNestedScroll(CoordinatorLayout coordinatorLayout, AppBarLayout child, View target, int dxConsumed, int dyConsumed, int dxUnconsumed, int dyUnconsumed, int type, int[] consumed) {
//        Log.i("onNestedScroll", "dyConsumed = " + dyConsumed + "   " + "dyUnconsumed = " + dyUnconsumed);
        if (dyUnconsumed<0 && isTop(coordinatorLayout, child) && currTabLayoutHeight < maxTabLayoutHeight) {
            //tab吸顶状态向下拉
            handleTabHeight(child, dyUnconsumed);
        } else {
            super.onNestedScroll(coordinatorLayout, child, target, dxConsumed, dyConsumed, dxUnconsumed, dyUnconsumed, type, consumed);
        }
    }

    /**
     * 反射调用appBarLayout的protected的方法
     * @param appBarLayout
     * @param methodName
     * @return
     */
    private int invokeVoidMethod(AppBarLayout appBarLayout, String methodName) {
        try {
            Class aClass = appBarLayout.getClass();
            Method m = aClass.getDeclaredMethod(methodName);
            m.setAccessible(true);
            return (int) m.invoke(appBarLayout);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 是否已经吸顶
     * @param coordinatorLayout
     * @param child
     * @return
     */
    private boolean isTop(CoordinatorLayout coordinatorLayout, AppBarLayout child) {
        View view = child.findViewById(R.id.homeTabLayout);
        int[] viewPosition = new int[2];
        int[] coordinatorPosition = new int[2];
        view.getLocationInWindow(viewPosition);
        coordinatorLayout.getLocationInWindow(coordinatorPosition);
        return viewPosition[1] == coordinatorPosition[1];
    }


    /**
     * 处理吸顶后TabLayout高度
     * @param appBarLayout
     * @param dy
     */
    private void handleTabHeight(AppBarLayout appBarLayout, int dy) {
        if (tabLayout == null) tabLayout = appBarLayout.findViewById(R.id.homeTabLayout);
        int tempHeight = currTabLayoutHeight - dy;
        if (tempHeight < minTabLayoutHeight) tempHeight = minTabLayoutHeight;
        if (tempHeight > maxTabLayoutHeight) tempHeight = maxTabLayoutHeight;
        currTabLayoutHeight = tempHeight;
        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) tabLayout.getLayoutParams();
        lp.height = currTabLayoutHeight;
        tabLayout.setLayoutParams(lp);
        handleTabLayoutUI();
    }

    /**
     * 处理tabLayout渐变
     */
    private void handleTabLayoutUI() {
        float radio = (maxTabLayoutHeight - currTabLayoutHeight) * 1.0f / scaleHeight;
        tabLayout.setUI(radio);
    }
}
