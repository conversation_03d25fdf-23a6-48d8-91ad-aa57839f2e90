package com.ybmmarket20.view.databinding

import android.graphics.Color
import android.view.View
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybmmarket20.utils.ImageUtil

/**
 * 加载图片
 */
//@BindingAdapter(value = ["imageUrl"])
fun imageUrl(view: ImageView, imageUrl: String?) {
    ImageUtil.load(
        view.context,
        imageUrl,
        view
    )
}

/**
 * 加载图片
 */
//@BindingAdapter(value = ["imageUrlNoPlace"])
fun imageUrlNoPlace(view: ImageView, imageUrl: String?) {
    ImageUtil.loadNoPlace(
        view.context,
        imageUrl,
        view
    )
}

/**
 * RecyclerView设置Adapter
 */
//@BindingAdapter(value = ["recyclerAdapter"])
fun configRecycler(view: RecyclerView, recyclerAdapter: YBMBaseAdapter<in Any>) {
    view.adapter = recyclerAdapter
}

/**
 * 设置view背景色
 * 入参 "#FFFFFF"
 */
//@BindingAdapter(value = ["backgroundColorStr"])
fun backgroundColorStr(view: View, colorStr: String?) {
    colorStr?.let { view.setBackgroundColor(Color.parseColor(it)) }?: view.setBackgroundColor(Color.parseColor("#00000000"))
}