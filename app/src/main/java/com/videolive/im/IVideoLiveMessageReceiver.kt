package com.videolive.im

import com.ybmmarket20.bean.im.VideoLiveMessageJoinMemberBean
import com.ybmmarket20.bean.im.VideoLiveMessageCouponBean
import com.ybmmarket20.bean.im.VideoLiveMessageGoodsBean
import com.ybmmarket20.bean.im.VideoLiveMessageStatusBean
import com.ybmmarket20.utils.im.core.data.MemberInfo

/**
 * <AUTHOR>
 * 直播间业务消息接收
 */

interface IVideoLiveMessageReceiver {

    /**
     * 直播间状态变更
     */
    fun onVideoLiveStateChange(messageStatusBean: VideoLiveMessageStatusBean?)

    /**
     * 直播间分发优惠券
     */
    fun onVideoLiveDispatchCoupon(messageCouponBean: VideoLiveMessageCouponBean?)

    /**
     * 直播间分发优惠商品
     */
    fun onVideoLiveDispatchDiscountGoods(messageGoodsBean: VideoLiveMessageGoodsBean?)

    /**
     * 用户进入直播间
     */
    fun onVideoLiveMemberEnter(messageEnterRoom: VideoLiveMessageJoinMemberBean?)

    /**
     * 用户离开直播间
     */
    //fun onVideoLiveMemberLeave(messageLeaveRoom: VideoLiveMessageJoinMemberBean?)

    /**
     * 点赞
     */
    fun onVideoLiveFavor(messageFavor: VideoLiveMessageJoinMemberBean?)

    /**
     * 接收文本消息
     */
    fun onReceiveGroupTextMessage(msgID: String?, groupID: String?, sender: MemberInfo?, text: String?)

}