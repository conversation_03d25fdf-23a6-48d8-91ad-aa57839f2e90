package com.videolive

import android.util.Log
import com.apkfuns.logutils.LogUtils
import com.videolive.im.IVideoLiveMessageReceiver
import com.videolive.im.VideoLiveMessageReceiver
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.ECLiveConstants
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.im.IMManager
import com.ybmmarket20.utils.im.core.data.IMMessage
import com.ybmmarket20.utils.im.core.data.MemberInfo
import com.ybmmarket20.utils.im.core.impl.BaseIMCoreCallback
import com.ybmmarket20.utils.im.core.impl.BaseIMCoreSimpleMessageCallback
import com.ybmmarket20.utils.im.core.impl.TencentIMStrategy

/**
 * <AUTHOR>
 * 实现IM的基类
 */

abstract class IMActivity : BaseActivity() {

    val imCallback: IMCallback by lazy { IMCallback() }
    private val imSimpleMessageCallback: IMSimpleMessageCallback by lazy { IMSimpleMessageCallback() }
    private val imVideoLiveMessageReceiver: VideoLiveMessageReceiver by lazy { VideoLiveMessageReceiver() }
    var groupId: String? = null
    val mIMManger: IMManager by lazy { IMManager(TencentIMStrategy()) }

    /**
     * 初始化 IM
     */
    fun initIM(sdkAppID: Int, groupId: String?) {
        this.groupId = groupId
        mIMManger.initStrategy(this, sdkAppID, Log.DEBUG, imCallback)
        mIMManger.setReceiveGroupListener(imCallback)
        mIMManger.setReceiveMessageListener(imSimpleMessageCallback)
        imVideoLiveMessageReceiver.setVideoLiveMessageReceiver(getVideoLiveMessageCallback())
    }

    abstract fun getVideoLiveMessageCallback(): IVideoLiveMessageReceiver?

    /**
     * im加载失败（初始化失败、登录失败、加入房间失败）
     */
    abstract fun onIMLoadFail()

    abstract fun onIMLoadSuccess()

    abstract fun onIMQuiteGroupdSuccess()

    abstract fun getSig(): String?

    /**
     * 文本消息和自定义消息回调
     */
    inner class IMSimpleMessageCallback : BaseIMCoreSimpleMessageCallback() {
        override fun onReceiveGroupTextMessage(msgID: String?, groupID: String?, sender: MemberInfo?, text: String?) {
            super.onReceiveGroupTextMessage(msgID, groupID, sender, text)
            imVideoLiveMessageReceiver.onReceiveTextGroupMessage(msgID, groupID, sender, text)
        }

        override fun onReceiveGroupCustomMessage(msgID: String?, groupID: String?, sender: MemberInfo?, customData: ByteArray?) {
            super.onReceiveGroupCustomMessage(msgID, groupID, sender, customData)
            imVideoLiveMessageReceiver.onReceiveCustomGroupMessage(msgID, groupID, sender, customData)
        }
    }

    /**
     * im回调
     */
    inner class IMCallback : BaseIMCoreCallback() {

        override fun onInitConnectSuccess() {
            super.onInitConnectSuccess()
            val userId = SpUtil.readString(ECLiveConstants.IM_USER_ID, "")
            val userSig = SpUtil.readString(ECLiveConstants.IM_USER_SIG, "")
            mIMManger.login(userId, userSig, this)
        }

        override fun onInitConnectFail(code: Int, msg: String?) {
            super.onInitConnectFail(code, msg)
            dismissProgress()
            onIMLoadFail()
        }

        override fun onLoginSuccess() {
            super.onLoginSuccess()
            LogUtils.e("im login success")
            val userId = SpUtil.readString(ECLiveConstants.IM_USER_ID, "")
            mIMManger.joinGroup(userId, groupId, "xxxx", this)
        }

        override fun onLoginFail(code: Int, msg: String?) {
            super.onLoginFail(code, msg)
            dismissProgress()
            mIMManger.unInitStrategy(this)
            onIMLoadFail()
        }

        override fun onUserSigExpired() {
            super.onUserSigExpired()
        }

        override fun onSelfInfoUpdated(memberInfo: MemberInfo?) {
            super.onSelfInfoUpdated(memberInfo)
            LogUtils.e("im userid = ${memberInfo?.userId} ${memberInfo?.avatar} ${memberInfo?.nickName}  ${memberInfo?.isSelf}")
        }

        override fun onJoinGroupSuccess() {
            super.onJoinGroupSuccess()
            dismissProgress()
            onIMLoadSuccess()
        }

        override fun onJoinGroupFail(code: Int, msg: String?) {
            super.onJoinGroupFail(code, msg)
            dismissProgress()
            mIMManger.unInitStrategy(this)
            onIMLoadFail()
        }

        override fun onQuiteGroupSuccess() {
            super.onQuiteGroupSuccess()
            onIMQuiteGroupdSuccess()
        }

        override fun onQuiteGroupFail(code: Int, msg: String?) {
            super.onQuiteGroupFail(code, msg)
        }

        override fun onSendGroupTextMessageSuccess(imMessage: IMMessage) {
            super.onSendGroupTextMessageSuccess(imMessage)
            // 发送消息成功
            LogUtils.i("im 个人发送消息成功 text = ${imMessage?.text}")
            val memberInfo = MemberInfo(imMessage?.senderUserId, imMessage?.senderNickName, imMessage?.senderAvatar, 0, true)
            imVideoLiveMessageReceiver.onReceiveTextGroupMessage(imMessage?.msgId, imMessage?.groupId, memberInfo, imMessage?.text)
        }

        override fun onSendGroupTextMessageFail(code: Int, msg: String?) {
            super.onSendGroupTextMessageFail(code, msg)
        }

        override fun onSendGroupCustomMessageSuccess(imMessage: IMMessage) {
            super.onSendGroupCustomMessageSuccess(imMessage)
        }

        override fun onSendGroupCustomMessageFail(code: Int, msg: String?) {
            super.onSendGroupCustomMessageFail(code, msg)
        }

        override fun onReceiveCustomSystemMessage(groupId: String?, customData: ByteArray?) {
            imVideoLiveMessageReceiver.onReceiveCustomSystemMessage(groupId, customData)
        }
    }
}