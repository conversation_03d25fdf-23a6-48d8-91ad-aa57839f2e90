package com.ybm.app.common;

import android.os.Handler;
import android.text.TextUtils;

import com.litesuits.go.OverloadPolicy;
import com.litesuits.go.SchedulePolicy;
import com.litesuits.go.SmartExecutor;
import com.litesuits.go.utils.GoUtil;

import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.RunnableFuture;

/**
 * 异步任务管理，全局唯一，控制任务
 */
public class SmartExecutorManager {
    private static SmartExecutorManager mInstance;
    private SmartExecutor smartExecutor;
    private int MAXCORESIZE = GoUtil.getCoresNumbers();
    private int MAXQUEUESIZE = MAXCORESIZE * 32;
    private Handler mHandler;
    private static ConcurrentHashMap<String, String> runRunnable = new ConcurrentHashMap<>();

    private SmartExecutorManager() {
        smartExecutor = new SmartExecutor();
        smartExecutor.setSchedulePolicy(SchedulePolicy.FirstInFistRun);
        smartExecutor.setQueueSize(MAXQUEUESIZE);
        smartExecutor.setCoreSize(MAXCORESIZE);
        smartExecutor.setOverloadPolicy(OverloadPolicy.DiscardOldTaskInQueue);
        mHandler = BaseYBMApp.handler;
    }

    public Handler getHandler() {
        return mHandler;
    }

    /**
     * @return
     */
    public static SmartExecutorManager getInstance() {
        if (null == mInstance) {
            synchronized (SmartExecutorManager.class) {
                if (null == mInstance) {
                    mInstance = new SmartExecutorManager();
                }
            }
        }
        return mInstance;
    }

    public Future<?> submit(Runnable task) {
        return smartExecutor.submit(task);
    }

    public <T> Future<T> submit(Runnable task, T result) {
        return smartExecutor.submit(task, result);
    }

    public <T> Future<T> submit(Callable<T> task) {
        return smartExecutor.submit(task);
    }

    public <T> void submit(RunnableFuture<T> task) {
        this.execute(task);
    }

    public void execute(final Runnable command) {
        smartExecutor.execute(command);
    }

    public void executeUI(final Runnable command) {
        mHandler.post(command);
    }

    public void executeUI(final Runnable command, long delayed) {
        mHandler.postDelayed(command, delayed);
    }

    //添加的任务只会执行一个
    public void executeOnly(SmartOnlyRunnable runnable) {
        if (runnable == null || TextUtils.isEmpty(runnable.getKey())) {
            return;
        }
        if (runRunnable.containsKey(runnable.getKey())) {
            return;
        }
        runRunnable.put(runnable.getKey(), "");
        execute(runnable);
    }

    public void clearOnly() {
        runRunnable.clear();
    }

    public static abstract class SmartOnlyRunnable implements Runnable {
        private String key;

        public SmartOnlyRunnable(String key) {
            this.key = key;
        }

        @Override
        public void run() {
            start();
            end();
        }

        public abstract void start();

        private final synchronized void end() {
            runRunnable.remove(getKey());
        }

        public String getKey() {
            return key;
        }
    }
}
