package com.ybm.app.utils;

import com.tencent.bugly.library.Bugly;
import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.bugly.BuglyLogManager;
import com.ybmmarket20.bugly.BuglyManager;

/**
 * bug 收集与上传
 */
public class BugUtil {
    private static int SCENE_USER = 30507;
    private static int SCENE_SDK = 30508;

    public BugUtil() {
    }

    /**
     * 捕获的java异常
     * @param e
     */
    public static void sendBug(Throwable e) {
        //todo 生产环境上报 测试环境打印日志
        BuglyManager.INSTANCE.catchJavaError(e, !BaseYBMApp.getApp().isDebug() && BaseYBMApp.getApp().isAgreedPrivacy(),null,null);
    }

    public static void sendBug(Throwable e,String extMsg,String extData) {
        //todo 生产环境上报 测试环境打印日志
        BuglyManager.INSTANCE.catchJavaError(e, !BaseYBMApp.getApp().isDebug() && BaseYBMApp.getApp().isAgreedPrivacy(),extMsg,extData);
    }

    /**
     * 初始化异常监听
     *
     * @param buglyAppId
     * @param appKey
     */
    public static void initBug(String buglyAppId, String appKey) {
        BuglyManager.INSTANCE.initBug(BaseYBMApp.getAppContext(), buglyAppId, appKey, null);
    }

    /**
     * 初始化日志采集相关
     *
     * @param buglyAppId
     * @param appKey
     */
    public static void initDiagConfig(String buglyAppId, String appKey) {
        BuglyLogManager.INSTANCE.initDiagConfig(BaseYBMApp.getAppContext(), buglyAppId, appKey);
    }

    /**
     * 更新绑定的用户
     * @param userId
     */
    public static void updateUserId(String userId) {
        Bugly.updateUserId(BaseYBMApp.getAppContext(), userId);
    }

}
