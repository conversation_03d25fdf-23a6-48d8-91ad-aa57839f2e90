package com.ybm.app.utils;

import android.os.Environment;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.io.StreamCorruptedException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 文件存储工具类。
 */
public class FileStorageUtil {

	public static boolean writeObject(File file, Object object) {
		try {
			FileOutputStream fs = new FileOutputStream(file);
			ObjectOutputStream os = new ObjectOutputStream(fs);
			os.writeObject(object);
			os.flush();
			fs.flush();
			os.close();
			fs.close();
			return true;
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return false;
	}

	public static Object readObject(File file) {
		try {
			file.length();
			FileInputStream fs = new FileInputStream(file);
			ObjectInputStream os = new ObjectInputStream(fs);
			Object object = os.readObject();
			os.close();
			fs.close();
			return object;
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (StreamCorruptedException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static boolean writeData(File file, byte[] data) {
		try {
			FileOutputStream fs = new FileOutputStream(file);
			DataOutputStream os = new DataOutputStream(fs);
			os.write(data);
			os.flush();
			fs.flush();
			os.close();
			fs.close();
			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public static boolean writeData(File file, String content) {
		return writeData(file, content.getBytes());
	}

	public static byte[] readData(File file) throws IOException {
		return readData(new FileInputStream(file));
	}

	public static byte[] readData(InputStream is) throws IOException {
		BufferedInputStream in = new BufferedInputStream(is);
		ByteArrayOutputStream out = new ByteArrayOutputStream(1024);

		byte[] temp = new byte[1024];
		int size = 0;
		while ((size = in.read(temp)) != -1) {
			out.write(temp, 0, size);
		}
		in.close();

		return out.toByteArray();
	}

	/**
	 * 用于ObjectList的序列化
	 * 
	 * @param <T>
	 * 
	 * @param file
	 * @param lists
	 * @return
	 * @throws Exception
	 */
	public static <T> boolean writeObjectList(File file, List<T> lists) {
		if (lists == null || lists.size() <= 0) {
			return false;
		}
		try {
			FileOutputStream fs = new FileOutputStream(file, true);
			ObjectOutputStream os = null;
			if (file.length() < 1) {
				os = new ObjectOutputStream(fs);
			} else {
				os = new AppendableObjectOutputStream(fs);
			}
			for (T object : lists) {
				os.writeObject(object);
			}
			os.flush();
			fs.flush();
			os.close();
			fs.close();
			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	/**
	 * 读取序列化的List<Object>
	 * 
	 * @param <T>
	 * 
	 * @param file
	 * @return
	 */
	private static FileInputStream fs;
	private static ObjectInputStream os;

	public static <T> List<T> readListObject(File file) {
		List<T> lists = new ArrayList<T>();
		try {
			fs = new FileInputStream(file);
			os = new ObjectInputStream(fs);
			Object obj = null;
			while ((obj = os.readObject()) != null) {
				lists.add((T) obj);
			}
			os.close();
			fs.close();
		} catch (Exception e) {
			try {
				os.close();
				fs.close();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		}
		return lists;
	}

	/**
	 * 用于writeObject时 不写入header 方便反序列化时正常读取
	 * 
	 * <AUTHOR>
	 * 
	 */
	static class AppendableObjectOutputStream extends ObjectOutputStream {

		protected AppendableObjectOutputStream() throws IOException {
			super();
		}

		public AppendableObjectOutputStream(OutputStream output)
				throws IOException {
			super(output);
		}

		@Override
		protected void writeStreamHeader() throws IOException {
			// do nothing for 方便反序列化时正常读取
		}

	}

	/**
	 * 从字节流写入到文件中
	 * 
	 * @param file
	 *            要保存到的文件
	 * @param is
	 *            输入流
	 * @return
	 * @throws IOException
	 */
	public static void writeInputStream(File file, InputStream is)
			throws IOException {
		if (is == null || is.available() == 0) {
			throw new IOException();
		}
		OutputStream out = new FileOutputStream(file);
		byte buffer[] = new byte[1024 * 8];
		int len = 0;
		while ((len = is.read(buffer)) != -1) {
			out.write(buffer, 0, len);
		}
		out.flush();
		out.close();
		is.close();
	}

	/**
	 * 递归删除文件（夹）。
	 * 
	 * @param f
	 */
	public static void deleteFiles(File f) {
		if (null == f || !f.exists()) {
			return;
		}

		if (f.isDirectory()) {
			for (File child : f.listFiles()) {
				deleteFiles(child);
			}
		}

		f.delete();
	}

	/**
	 * 递归删除文件或者文件夹下面文件（保留空文件夹）。
	 * 
	 * @param file
	 */
	public static boolean deleteFoder(File file) {
		if (null != file && file.exists()) { // 判断文件是否存在
			if (file.isFile()) { // 判断是否是文件
				file.delete(); // delete()方法 你应该知道 是删除的意思;
			} else if (file.isDirectory()) { // 否则如果它是一个目录
				File files[] = file.listFiles(); // 声明目录下所有的文件 files[];
				if (files != null) {
					for (int i = 0; i < files.length; i++) { // 遍历目录下所有的文件
						// files[i].delete();
						deleteFoder(files[i]); // 把每个文件 用这个方法进行迭代
					}
				}
			}
		}
		return true;
	}

	/**
	 * @param ips
	 *            输入流
	 * @param destFile
	 *            目标文件
	 */
	public static void copyFile(InputStream ips, File destFile) {
		if (!destFile.exists()) {
			File parentFile = destFile.getParentFile();
			if (!parentFile.exists()) {
				parentFile.mkdirs();
			}
		}
		try {
			FileOutputStream fos = new FileOutputStream(destFile);
			byte[] bytes = new byte[512];
			int len = 0;
			while ((len = ips.read(bytes)) != -1) {
				fos.write(bytes, 0, len);
			}
			fos.close();
			ips.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * @param sourceFile
	 *            源文件
	 * @param destFile
	 *            目标文件
	 */
	public static void copyFile(File sourceFile, File destFile) {
		try {
			copyFile(new FileInputStream(sourceFile), destFile);
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/**
	 * 判断SD卡是否加载
	 */
	public static boolean isSDCardMounted() {
		return Environment.MEDIA_MOUNTED.equals(Environment
				.getExternalStorageState());
	}

	/**
	 * 递归创建目录
	 * 
	 * @param dirPath
	 *            需创建的目标路径，可以为多重目录
	 * @return 是否创建成功
	 */
	public static boolean createDirectory(String dirPath) {
		if (dirPath == null)
			return false;
		File dir = new File(dirPath);
		if (!dir.exists()) {
			return dir.mkdirs();
		}
		return true;
	}

	public static boolean checkSdcardAvailable() {
		return Environment.getExternalStorageState().equals(
				Environment.MEDIA_MOUNTED);
	}

	/**
	 * 解压缩zip文件到指定路径
	 * 
	 * @param is
	 *            zip格式的InputStream文件
	 * @param destFile
	 * @return
	 */
	public static boolean decompressZip(InputStream is, File destFile) {
		byte[] buffer = new byte[1024];
		try {
			ZipInputStream zis = new ZipInputStream(is);
			ZipEntry ze = zis.getNextEntry();
			while (ze != null) {
				String fileName = ze.getName();
				if (fileName != null && destFile != null
						&& fileName.equals(destFile.getName())) {
					FileOutputStream fos = new FileOutputStream(destFile);
					int len;
					while ((len = zis.read(buffer)) > 0) {
						fos.write(buffer, 0, len);
					}
					fos.close();
					ze = zis.getNextEntry();
				}
			}
			zis.closeEntry();
			zis.close();
		} catch (Exception e) {
			return false;
		}

		return true;
	}

	/**
	 * 将某路径下所有文件按照创建时间排列
	 * 
	 * @param folderPath
	 * @param isAsc
	 *            true升序; false降序
	 * @return 升序排列的文件列表，或者null
	 */
	public static List<File> getFilesSortedByDate(String folderPath,
			final boolean isAsc) {
		if (folderPath == null) {
			return null;
		}
		File dir = new File(folderPath);
		if (dir.exists() && dir.isDirectory()) {
			List<File> list = Arrays.asList(dir.listFiles());
			Collections.sort(list, new Comparator<File>() {
				public int compare(File o1, File o2) {
					if (isAsc) {
						return (o1.lastModified() > o2.lastModified()) ? 1 : -1;
					} else {
						return (o1.lastModified() <= o2.lastModified()) ? 1
								: -1;
					}
				}
			});
			return list;
		}
		return null;
	}

}
