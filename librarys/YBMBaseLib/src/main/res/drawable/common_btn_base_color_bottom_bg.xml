<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="false">
        <shape
            android:shape="rectangle">
            <solid android:color="@color/btn_gray" />
            <corners android:bottomRightRadius="@dimen/dialog_radius" android:bottomLeftRadius="@dimen/dialog_radius"/>
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape
            android:shape="rectangle">
            <solid android:color="@color/back_orange" />
            <corners android:bottomRightRadius="@dimen/dialog_radius" android:bottomLeftRadius="@dimen/dialog_radius"/>
        </shape>
    </item>
    <item>
        <shape
            android:shape="rectangle">
            <solid android:color="@color/home_back_selected" />
            <corners android:bottomRightRadius="@dimen/dialog_radius" android:bottomLeftRadius="@dimen/dialog_radius"/>
        </shape>
    </item>
</selector>