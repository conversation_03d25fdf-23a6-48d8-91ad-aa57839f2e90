<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/dialog_shadow"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/picture_prompt"
            android:textColor="#ff572e"
            android:textSize="16sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@color/line_color" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1.5"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                android:text="@string/picture_prompt_content"
                android:textColor="#53575e"
                android:textSize="15sp" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="bottom"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="1dip"
                android:background="@color/line_color" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_cancel"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:background="@drawable/btn_left_bottom_selector"
                    android:text="@string/picture_cancel"
                    android:textColor="#529BeA" />

                <TextView
                    android:layout_width="1dip"
                    android:layout_height="match_parent"
                    android:background="@color/line_color" />

                <Button
                    android:id="@+id/btn_commit"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:background="@drawable/btn_right_bottom_selector"
                    android:text="@string/picture_confirm"
                    android:textColor="#529BeA" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
