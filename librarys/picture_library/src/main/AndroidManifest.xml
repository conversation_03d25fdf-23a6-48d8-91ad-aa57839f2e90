<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.luck.picture.lib">

    <application android:theme="@style/AppTheme">

        <provider
            android:name="com.luck.picture.lib.PictureFileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <activity
            android:name=".PictureSelectorActivity"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name=".PicturePreviewActivity" />
        <activity
            android:name=".PictureVideoPlayActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="sensor" />
        <activity android:name=".PictureExternalPreviewActivity" />
        <activity android:name="com.yalantis.ucrop.UCropActivity" />
        <activity android:name="com.yalantis.ucrop.PictureMultiCuttingActivity" />
        <activity android:name=".PicturePlayAudioActivity" />
        <activity android:name=".PictureNetPreviewActivity" />


    </application>

</manifest>