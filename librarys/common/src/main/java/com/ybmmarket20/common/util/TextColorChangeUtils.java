package com.ybmmarket20.common.util;

import android.content.Context;
import androidx.core.content.ContextCompat;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.widget.TextView;

/**
 * @author: yuhaibo
 * @time: 2018/11/21 上午9:50.
 * projectName: YBMMarket.
 * Description: 一段文字多种颜色
 */
public class TextColorChangeUtils {
    /***
     *通过Resources内的颜色实现文字变色龙
     *
     * @param context 上下文
     * @param textView 文字变色控件
     * @param textColor  文字固有颜色
     * @param startColor 开始半段文字color
     * @param endColor 结束半段 文字color
     * @param startStart 前半段开始变色文字下标
     * @param startEnd 前半段结束变色文字下标
     * @param endStart 后半段开始变色文字下标
     * @param endEnd 后半段结束变色文字下标
     * @param text 变色的文字内容
     * @return 返回变色结果
     */

    public static void setInterTextColorForResources(Context context, TextView textView, int textColor, int startColor,
                                                     int endColor, int startStart, int startEnd, int endStart, int endEnd, String text) {
        if (context == null || textView == null || text == null) {
            return;
        }
        textView.setTextColor(ContextCompat.getColor(context, textColor));
        SpannableStringBuilder style = new SpannableStringBuilder(text);
        style.setSpan(new ForegroundColorSpan(context.getResources().getColor(startColor)), startStart, startEnd, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        style.setSpan(new ForegroundColorSpan(context.getResources().getColor(endColor)), endStart, endEnd, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        textView.setText(style);
    }

    /**
     * 设置textView显示不同颜色的text
     *
     * @param context
     * @param textView
     * @param text1
     * @param changetext
     * @param text2
     * @param color
     */
    public static void setInterTextColor(Context context, TextView textView, String text1, String changetext, String text2, int color) {
        if (textView == null || text1 == null || changetext == null || text2 == null || context == null || TextUtils.isEmpty(changetext)) {
            return;
        }
        SpannableString spannableString = new SpannableString(text1 + changetext + text2);
        //spannableString.setSpan(new BackgroundColorSpan(Color.GREEN),text1.length(), text1.length() + changetext.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE); //设置指定字体的背景色
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(context, color)), text1.length(), text1.length() + changetext.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE); //设置指定字体的颜色
        textView.setText(spannableString);
    }

}
