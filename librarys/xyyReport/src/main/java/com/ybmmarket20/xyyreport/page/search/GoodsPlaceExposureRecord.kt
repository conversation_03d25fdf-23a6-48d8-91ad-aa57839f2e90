package com.ybmmarket20.xyyreport.page.search

class GoodsPlaceExposureRecord {

    private val mGoodsPlaceExposureRecord = hashSetOf<String>()

    companion object {

        private val mRecordInstanceMap = hashMapOf<Any, GoodsPlaceExposureRecord>()

        @JvmStatic
        @Synchronized
        fun get(obj: Any?): GoodsPlaceExposureRecord {
            if (obj == null) return GoodsPlaceExposureRecord()
            if (!mRecordInstanceMap.contains(obj)) {
                val instance = GoodsPlaceExposureRecord()
                mRecordInstanceMap[obj] = instance
            }
            return mRecordInstanceMap[obj]!!
        }

        @JvmStatic
        @Synchronized
        fun release(obj: Any?) {
            if (obj == null) return
            mRecordInstanceMap.remove(obj)
        }
    }

    fun containsRecord(key: String?): Boolean {
        if (key == null) return false
        return mGoodsPlaceExposureRecord.contains(key)
    }

    /**
     * 添加缓存
     */
    fun markRecord(label: String?) {
        if (label == null) return
        mGoodsPlaceExposureRecord.add(label)
    }

    fun clearRecordByKeyList(keyList: List<String>?) {
        if (keyList == null) return
        keyList.forEach(::clearRecordByKey)
    }

    fun clearRecordByKey(key: String?) {
        if (key == null) return
        mGoodsPlaceExposureRecord.remove(key)
    }


}